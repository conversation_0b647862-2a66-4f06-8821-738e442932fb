"""
Test cases for robust timestamp format validation in AccountingApiAgent.

This test suite validates the _validate_timestamp_format method and 
generate_reference_key method to ensure proper handling of various 
timestamp formats and edge cases.
"""

import unittest
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from services.tools.data_processor.accounting_api_agent import AccountingApiAgent


class TestTimestampValidation(unittest.TestCase):
    """Test cases for timestamp format validation."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.agent = AccountingApiAgent()
    
    def test_valid_24_hour_formats(self):
        """Test valid 24-hour timestamp formats."""
        test_cases = [
            ("09:30", "09:30:00"),
            ("9:30", "09:30:00"),
            ("23:59", "23:59:00"),
            ("00:00", "00:00:00"),
            ("12:00", "12:00:00"),
            ("09:30:45", "09:30:45"),
            ("9:30:45", "09:30:45"),
            ("23:59:59", "23:59:59"),
            ("00:00:00", "00:00:00"),
            ("01:05:30", "01:05:30"),
        ]
        
        for input_time, expected in test_cases:
            with self.subTest(input_time=input_time):
                result = self.agent._validate_timestamp_format(input_time)
                self.assertEqual(result, expected, 
                               f"Failed for input '{input_time}', expected '{expected}', got '{result}'")
    
    def test_valid_12_hour_formats(self):
        """Test valid 12-hour timestamp formats with AM/PM."""
        test_cases = [
            ("9:30 AM", "09:30:00"),
            ("09:30 AM", "09:30:00"),
            ("12:00 AM", "00:00:00"),  # Midnight
            ("12:00 PM", "12:00:00"),  # Noon
            ("1:00 PM", "13:00:00"),
            ("11:59 PM", "23:59:00"),
            ("9:30:45 AM", "09:30:45"),
            ("9:30:45 PM", "21:30:45"),
            ("12:30:15 AM", "00:30:15"),
            ("12:30:15 PM", "12:30:15"),
            # Test case insensitive AM/PM
            ("9:30 am", "09:30:00"),
            ("9:30 pm", "21:30:00"),
            ("9:30 Am", "09:30:00"),
            ("9:30 Pm", "21:30:00"),
        ]
        
        for input_time, expected in test_cases:
            with self.subTest(input_time=input_time):
                result = self.agent._validate_timestamp_format(input_time)
                self.assertEqual(result, expected, 
                               f"Failed for input '{input_time}', expected '{expected}', got '{result}'")
    
    def test_invalid_formats(self):
        """Test invalid timestamp formats that should return empty string."""
        invalid_cases = [
            "",  # Empty string
            "   ",  # Only whitespace
            "25:00",  # Invalid hour
            "12:60",  # Invalid minute
            "12:30:60",  # Invalid second
            "24:00",  # Invalid hour (24 is not valid in 24-hour format)
            "9:30 XM",  # Invalid AM/PM
            "13:30 PM",  # Invalid hour for 12-hour format
            "0:30 AM",  # Invalid hour for 12-hour format
            "12:30:45:00",  # Too many components
            "9",  # Missing minute
            "9:",  # Missing minute
            ":30",  # Missing hour
            "9-30",  # Wrong separator
            "9.30",  # Wrong separator
            "abc:def",  # Non-numeric
            "9:30:45 AM PM",  # Multiple AM/PM
            "25:30 AM",  # Invalid hour even for 12-hour
        ]
        
        for invalid_time in invalid_cases:
            with self.subTest(invalid_time=invalid_time):
                result = self.agent._validate_timestamp_format(invalid_time)
                self.assertEqual(result, "", 
                               f"Expected empty string for invalid input '{invalid_time}', got '{result}'")
    
    def test_whitespace_handling(self):
        """Test proper handling of whitespace in timestamps."""
        test_cases = [
            ("  9:30  ", "09:30:00"),
            (" 9:30 AM ", "09:30:00"),
            ("9:30:45   PM  ", "21:30:45"),
            ("\t12:00\t", "12:00:00"),
            ("\n23:59\n", "23:59:00"),
        ]
        
        for input_time, expected in test_cases:
            with self.subTest(input_time=repr(input_time)):
                result = self.agent._validate_timestamp_format(input_time)
                self.assertEqual(result, expected, 
                               f"Failed for input {repr(input_time)}, expected '{expected}', got '{result}'")
    
    def test_generate_reference_key_with_timestamp(self):
        """Test generate_reference_key method with various timestamp inputs."""
        vendor = "Test Vendor"
        date = "2024-01-15"
        invoice = "INV001"
        
        # Test with valid timestamp
        key1 = self.agent.generate_reference_key(vendor, date, "09:30", invoice)
        key2 = self.agent.generate_reference_key(vendor, date, "09:30:00", invoice)
        # Both should produce the same key since 09:30 normalizes to 09:30:00
        self.assertEqual(key1, key2)
        
        # Test with invalid timestamp (should still generate key without timestamp)
        key3 = self.agent.generate_reference_key(vendor, date, "invalid", invoice)
        key4 = self.agent.generate_reference_key(vendor, date, "", invoice)
        # Should be the same since invalid timestamp is ignored
        self.assertEqual(key3, key4)
        
        # Test with different valid timestamps should produce different keys
        key5 = self.agent.generate_reference_key(vendor, date, "10:30", invoice)
        self.assertNotEqual(key1, key5)
    
    def test_generate_reference_key_components(self):
        """Test that all components contribute to reference key uniqueness."""
        base_vendor = "Test Vendor"
        base_date = "2024-01-15"
        base_timestamp = "09:30"
        base_invoice = "INV001"
        
        # Base key
        base_key = self.agent.generate_reference_key(base_vendor, base_date, base_timestamp, base_invoice)
        
        # Different vendor should produce different key
        key_diff_vendor = self.agent.generate_reference_key("Different Vendor", base_date, base_timestamp, base_invoice)
        self.assertNotEqual(base_key, key_diff_vendor)
        
        # Different date should produce different key
        key_diff_date = self.agent.generate_reference_key(base_vendor, "2024-01-16", base_timestamp, base_invoice)
        self.assertNotEqual(base_key, key_diff_date)
        
        # Different timestamp should produce different key
        key_diff_timestamp = self.agent.generate_reference_key(base_vendor, base_date, "10:30", base_invoice)
        self.assertNotEqual(base_key, key_diff_timestamp)
        
        # Different invoice should produce different key
        key_diff_invoice = self.agent.generate_reference_key(base_vendor, base_date, base_timestamp, "INV002")
        self.assertNotEqual(base_key, key_diff_invoice)
    
    def test_edge_cases(self):
        """Test edge cases and boundary conditions."""
        # Test midnight in different formats
        midnight_24h = self.agent._validate_timestamp_format("00:00")
        midnight_12h = self.agent._validate_timestamp_format("12:00 AM")
        self.assertEqual(midnight_24h, midnight_12h)
        
        # Test noon in different formats
        noon_24h = self.agent._validate_timestamp_format("12:00")
        noon_12h = self.agent._validate_timestamp_format("12:00 PM")
        self.assertEqual(noon_24h, noon_12h)
        
        # Test boundary times
        self.assertEqual(self.agent._validate_timestamp_format("23:59:59"), "23:59:59")
        self.assertEqual(self.agent._validate_timestamp_format("00:00:00"), "00:00:00")
        
        # Test single digit components
        self.assertEqual(self.agent._validate_timestamp_format("1:05:09"), "01:05:09")


if __name__ == '__main__':
    unittest.main()
