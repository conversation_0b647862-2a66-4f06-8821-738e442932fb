FROM ubuntu:22.04

ENV DEBIAN_FRONTEND noninteractive

# install dependancies
RUN apt-get update -y && \
    apt-get install -y python3-pip python3-dev && \
    apt install -y git-all \
    libreoffice && \
    apt-get clean && rm -rf /var/lib/apt/lists/* && \
    apt update -y && apt-get install -y poppler-utils
 
RUN pip3 install pydantic==1.10.13

# We copy just the requirements.txt first to leverage Docker cache
COPY ./requirements.txt /app/requirements.txt

WORKDIR /app

RUN pip3 install -r requirements.txt

RUN pip install layernext-beta==3.20.5b2

COPY . /app/

EXPOSE 5082

ENTRYPOINT [ "python3" ]

CMD ["app.py"]
