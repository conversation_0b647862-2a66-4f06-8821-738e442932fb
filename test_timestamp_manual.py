#!/usr/bin/env python3
"""
Manual test script for timestamp validation functionality.
This script can be run independently to test the timestamp validation.
"""

import sys
import os
import re

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(__file__))


def validate_timestamp_format(timestamp: str) -> str:
    """
    Validate timestamp format and return normalized timestamp.
    Supports only 24-hour format:
    - HH:MM (24-hour format)
    - HH:MM:SS (24-hour format with seconds)
    - H:MM or H:MM:SS (single digit hour)

    Returns:
        str: Normalized timestamp in HH:MM:SS format, or empty string if invalid
    """
    if not timestamp:
        return ""

    # Remove extra whitespace
    timestamp = timestamp.strip()

    # Pattern for 24-hour format: HH:MM or HH:MM:SS (with optional single digit hour)
    pattern = r"^([0-1]?[0-9]|2[0-3]):([0-5][0-9])(?::([0-5][0-9]))?$"

    match = re.match(pattern, timestamp)
    if match:
        hour = int(match.group(1))
        minute = int(match.group(2))
        second = int(match.group(3)) if match.group(3) else 0

        # Validate time components
        if 0 <= hour <= 23 and 0 <= minute <= 59 and 0 <= second <= 59:
            return f"{hour:02d}:{minute:02d}:{second:02d}"

    # Invalid timestamp format
    return ""


def test_timestamp_validation():
    """Run comprehensive tests for timestamp validation."""
    print("Testing Timestamp Validation Implementation")
    print("=" * 50)

    # Test cases: (input, expected_output, description)
    test_cases = [
        # Valid 24-hour formats
        ("09:30", "09:30:00", "24-hour format with leading zero"),
        ("9:30", "09:30:00", "24-hour format without leading zero"),
        ("23:59", "23:59:00", "24-hour format boundary case"),
        ("00:00", "00:00:00", "24-hour format midnight"),
        ("12:00", "12:00:00", "24-hour format noon"),
        ("09:30:45", "09:30:45", "24-hour format with seconds"),
        ("1:05:09", "01:05:09", "Single digit hour with seconds"),
        ("23:59:59", "23:59:59", "24-hour format max time"),
        ("00:00:00", "00:00:00", "24-hour format min time"),
        # Whitespace handling
        ("  9:30  ", "09:30:00", "Whitespace around timestamp"),
        (" 23:45:30 ", "23:45:30", "Whitespace around timestamp with seconds"),
        # Invalid formats
        ("", "", "Empty string"),
        ("   ", "", "Only whitespace"),
        ("25:00", "", "Invalid hour (25)"),
        ("12:60", "", "Invalid minute (60)"),
        ("12:30:60", "", "Invalid second (60)"),
        ("24:00", "", "Invalid hour (24)"),
        ("9:30 AM", "", "12-hour format with AM (not supported)"),
        ("9:30 PM", "", "12-hour format with PM (not supported)"),
        ("9", "", "Missing minute"),
        ("9:", "", "Missing minute value"),
        (":30", "", "Missing hour"),
        ("9-30", "", "Wrong separator"),
        ("9.30", "", "Wrong separator (dot)"),
        ("abc:def", "", "Non-numeric values"),
        ("12:30:45:00", "", "Too many components"),
        ("-1:30", "", "Negative hour"),
        ("12:-5", "", "Negative minute"),
        ("12:30:-10", "", "Negative second"),
    ]

    passed = 0
    failed = 0

    for input_time, expected, description in test_cases:
        result = validate_timestamp_format(input_time)
        if result == expected:
            print(f"✓ PASS: {description}")
            print(f"  Input: '{input_time}' -> Output: '{result}'")
            passed += 1
        else:
            print(f"✗ FAIL: {description}")
            print(f"  Input: '{input_time}' -> Expected: '{expected}', Got: '{result}'")
            failed += 1
        print()

    print("=" * 50)
    print(f"Test Results: {passed} passed, {failed} failed")

    if failed == 0:
        print("🎉 All tests passed! Timestamp validation is working correctly.")
    else:
        print(f"❌ {failed} test(s) failed. Please review the implementation.")

    return failed == 0


if __name__ == "__main__":
    success = test_timestamp_validation()
    sys.exit(0 if success else 1)
