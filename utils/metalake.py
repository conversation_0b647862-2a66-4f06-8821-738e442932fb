import base64
import contextlib
from datetime import datetime
import io
from logging import Logger
import os
import time
import traceback
from bson import ObjectId
import layernext
from openai import AzureOpenAI, OpenAI
import json
import pathlib
import pickle

import openpyxl
import pandas as pd

# from data_dictionaries.func_dict import FUNC

# from importlib import import_module
from databases.mongo_manager import MongoDBmanager

from services.llm_client_factory import LLMClientFactory
from models.conversation import Conversation
from services.execution_service import ExecutionService
from services.data_dictionary_service import DataDictionaryService
from utils.constant import (
    LLM_PROVIDER_AZURE_OPENAI,
    SUB_LLM_MAX_ATTEMPTS_PER_SESSION,
    SUB_LLM_MAX_CSV_FILE_LENGTH,
    ExecutionMode,
    SubLLMOperationMode,
)
from utils.llm_utils import (
    check_unrelevent,
    get_python_actions,
)
from utils.logger import get_debug_logger
from utils.in_memory_data_manager import InMemoryDataManager
from utils.unstructured_data_processor import UnstructuredDataProcessor
from utils.excel_file_handler import update_excel_sheet_from_df

logger = get_debug_logger(
    "metalake", pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs/server.log")
)


class MetaLake:
    def __init__(self):
        # initialize layernext sdk client
        layernext_api_key = os.getenv("API_KEY")
        layernext_secret_key = os.getenv("SECRET_KEY")
        layernext_url = os.getenv("URL")
        self.layernext_client = layernext.LayerNextClient(layernext_api_key, layernext_secret_key, layernext_url)
        self.llm_name_unstructured_processing = os.getenv("MODEL_UNSTRUCTURED_PROCESSING")
        if not self.llm_name_unstructured_processing:
            raise ValueError("MODEL_UNSTRUCTURED_PROCESSING env variable not present")

        self.llm_api_provider = os.getenv("LLM_API_PROVIDER")
        self.company = os.getenv("COMPANY")
        self.llm_client_factory = LLMClientFactory()
        self.llm_client = self.llm_client_factory.get_llm_client(
            provider=self.llm_api_provider, temperature=os.getenv("TEMPERATURE")
        )

        # Functions that process each type of supported documents
        self.doc_info_extract_functions = {
            "pdf": self.extract_from_pdf,
            "csv": self.extract_from_csv,
            "default": self.extract_from_general_text,
        }
        self.execution_env = ExecutionService(ExecutionMode.TEST.value)
        self.unstructured_data_handler = UnstructuredDataProcessor(self.llm_client)

    def process_unstructured_data(
        self,
        chat_id,
        data_fetch_query,
        data_source_name,
        table_name,
        column_name,
        labelling_instruction,
        output_labels=[],
        primary_label=None,
    ):
        """
        Description:
            Processes and classifies unstructured data such as comments, notes, reviews, and feedback found in database tables using a Large Language Model (LLM).

        Parameters:
            chat_id (str): Unique identifier for the function call session.
            data_fetch_query (str): SQL to fetch the unstructured data, containing ORDER BY but excluding LIMIT and OFFSET
                key information.
            data_source_name (str): Name of the data source according to the data dictionary.
            table_name (str): Name of the table containing the unstructured data field.
            column_name (str): Name of the column containing the unstructured data.
            labelling_instruction (str): Instruction for the classification task based on user requirements.
            output_labels (list): List of labels to be used for classification.
            primary_label (str): Primary label to be used for classification.
        """
        # First fetch the unstructured data from DB using the given query
        chat_log = get_debug_logger(f"chat_{chat_id}", f"./logs/chat_{chat_id}.log")
        unstructured_data_list = run_sql_query(data_source_name, data_fetch_query, chat_log)
        chat_log.debug(
            f"metalake | process_unstructured_data | table = {table_name}, column = {column_name}: processing unstructured data, SQL = {data_fetch_query}, count = {len(unstructured_data_list)}"
        )
        return self.unstructured_data_handler.query_unstructured_data(
            chat_id,
            chat_log,
            unstructured_data_list,
            data_source_name,
            table_name,
            column_name,
            labelling_instruction,
            output_labels,
            primary_label,
        )

    def fill_unstructured_data_cache(
        self,
        chat_id,
        unstructured_data_list,
        data_source_name,
        table_name,
        column_name,
        labelling_instruction,
        output_labels=[],
        primary_label=None,
    ):
        self.unstructured_data_handler.generate_unstructured_data_cache(
            chat_id,
            unstructured_data_list,
            data_source_name,
            table_name,
            column_name,
            labelling_instruction,
            output_labels,
            primary_label,
        )

    """
        A function to extract key information from a PDF document
    """

    def extract_from_pdf(
        self,
        operation_mode: SubLLMOperationMode,
        document_chunks_dict,
        filtering_keywords,
        extraction_keys,
        instruction_str,
        chat_id,
    ):

        instruction_file = "llm_prompt_info_extraction_pdf.txt"  # Instructions for processing PDF
        with open("./instructions/" + instruction_file, "r", encoding="utf-8") as file:
            instructions = file.read()

        instructions = instructions.replace("<<filtering_keys>>", str(filtering_keywords))
        instructions = instructions.replace("<<extraction_keys>>", str(extraction_keys))
        instructions = instructions.replace("<<user_question>>", str(instruction_str))
        llm_prompt = []
        llm_prompt.append({"role": "system", "content": instructions})
        # llm_prompt.append({"role": "user", "content": f"Extract key data for this list of search keys {str(answering_keys)}."})
        llm_prompt.append({"role": "user", "content": "chunks dictionary :" + str(document_chunks_dict)})
        response = self.llm_client.get_llm_response(
            self,
            None,
            None,
            message_list=llm_prompt,
            model=self.llm_name_unstructured_processing,
            seed=1234,
            token_limit=4096,
        )
        # count tokens
        self.update_token_count(response, chat_id)
        llm_output = response.choices[0].message.content
        return llm_output

    """
        A function to extract key information from general text (eg: comments)
        data_with_key_dict_list: {dictionary list} unstructured data with key information as list of dictionaries
        filtering_keywords: Not used
    """

    def extract_from_general_text(
        self,
        operation_mode: SubLLMOperationMode,
        data_with_key_dict_list,
        filtering_keywords,
        extraction_keys,
        instruction_str,
        chat_id,
    ):
        chat_log = get_debug_logger(f"chat_{chat_id}", f"./logs/chat_{chat_id}.log")

        instruction_file = (
            "llm_prompt_info_extraction.txt"  # Instructions for processing general text in extraction mode
        )
        if operation_mode == SubLLMOperationMode.SUB_LLM_ANALYSIS.value:
            instruction_file = "llm_prompt_analysis.txt"
        with open("./instructions/" + instruction_file, "r", encoding="utf-8") as file:
            instructions = file.read()

        instructions = instructions.replace("<<extraction_keys>>", str(extraction_keys))
        instructions = instructions.replace("<<user_question>>", str(instruction_str))
        llm_prompt = []
        llm_output = []
        llm_prompt.append({"role": "system", "content": instructions})
        # llm_prompt.append({"role": "user", "content": f"Extract key data for this list of search keys {str(answering_keys)}."})
        llm_prompt.append({"role": "user", "content": "input :" + str(data_with_key_dict_list)})
        try:
            response = self.llm_client.get_llm_response(
                self,
                None,
                chat_log,
                message_list=llm_prompt,
                model=self.llm_name_unstructured_processing,
                seed=1234,
                token_limit=4096,
            )
            llm_output = response.choices[0].message.content
            # count tokens
            self.update_token_count(response, chat_id)
        except Exception as e:
            chat_log.warn(e)
        return llm_output

    """
        A function to extract key information from a CSV document
    """

    def extract_from_csv(
        self,
        operation_mode: SubLLMOperationMode,
        document_chunks_dict,
        filtering_keywords,
        extraction_keys,
        instruction_str,
        chat_id,
    ):

        import requests
        import uuid
        import csv

        chat_log = get_debug_logger(f"chat_{chat_id}", f"./logs/chat_{chat_id}.log")

        # Download the CSV file first
        download_url = document_chunks_dict["url"]
        if download_url is None:
            chat_log.debug("No url to download CSV")
            return None

        res = requests.get(download_url)
        if res.status_code != 200 or len(res.content) == 0:
            # Download failed or no data
            chat_log.debug("CSV data download failed")
            return None

        # By default, try to feed all CSV content to LLM - if it's less than limit
        if len(res.content) < SUB_LLM_MAX_CSV_FILE_LENGTH:
            chat_log.debug("Feeding whole file to LLM")
            return self.invoke_llm_for_csv_content(
                res.content, filtering_keywords, extraction_keys, instruction_str, chat_id
            )

        # First check whether this CSV is relevant for the question via LLM
        if not self.is_doc_relevant(document_chunks_dict, filtering_keywords, instruction_str, chat_id, chat_log):
            return "UN_RELEVANT"

        unique_id = str(uuid.uuid4())
        file_name = f"files/data_{unique_id}.csv"
        with open(file_name, "wb") as file:
            # Write the content of the response to the file
            file.write(res.content)

        # Get column names of CSV by processing the first line
        # Also get first 10 rows of CSV
        column_names = []
        first_rows = []
        with open(file_name, "r", encoding="utf-8") as read_obj:
            csv_reader = csv.reader(read_obj)
            list_of_rows = list(csv_reader)
            column_names = list_of_rows[0]
            first_rows = list_of_rows[1:10]

        instruction_file = (
            "llm_prompt_info_extraction_csv_python.txt"  # Instructions for processing CSV using Python code
        )
        with open("./instructions/" + instruction_file, "r", encoding="utf-8") as file:
            instructions = file.read()

        # instructions = instructions.replace("<<filtering_keys>>", str(filtering_keywords))
        instructions = instructions.replace("<<extraction_keys>>", str(extraction_keys))
        instructions = instructions.replace("<<user_question>>", str(instruction_str))

        # Generate a python code to extract data from this CSV file from LLM
        # This is executed in a loop until the data is properly output by code, maximum 2 attempts
        attempt_count = 0
        data_extracted = -1
        llm_prompt = []
        llm_prompt.append({"role": "system", "content": instructions})
        llm_prompt.append(
            {
                "role": "user",
                "content": f"Please generate a Python code that read the CSV file in path {file_name} and extract the data for user question.\n\
                           The CSV file has these columns: {column_names}\n\
                            The initial records of the CSV file are:\n{first_rows}",
            }
        )
        while data_extracted == -1 and attempt_count < SUB_LLM_MAX_ATTEMPTS_PER_SESSION:
            response = None
            try:
                chat_log.debug(f"SUB LLM INPUT:\n{llm_prompt}")
                response = self.llm_client.get_llm_response(
                    self,
                    None,
                    chat_log,
                    message_list=llm_prompt,
                    model=self.llm_name_unstructured_processing,
                    seed=1234,
                    token_limit=4096,
                )
            except Exception as e:
                chat_log.debug(f"Error calling sub LLM: {e}")
                return None

            attempt_count += 1
            self.update_token_count(response, chat_id)  # count tokens
            llm_output = response.choices[0].message.content
            chat_log.debug(f"SUB LLM OUTPUT:\n{llm_output}")
            # Check if LLM has given valid python code
            python_code = get_python_actions(llm_output)[0]
            if not python_code:
                # Abort and return failure
                chat_log.debug("Unable to generate Python code to extract info")
                return None

            chat_log.debug(f"SUB LLM Python Code:\n{python_code}")
            exec_output = self.execution_env.python_exec(action=python_code, chat_id=chat_id)
            chat_log.debug(f"SUB LLM Python output:\n{exec_output}")
            is_success = exec_output["success"]
            result = exec_output["output"]
            err_message = exec_output["message"]
            if is_success == False:
                # Error from Python code
                llm_prompt.append(
                    {
                        "role": "assistant",
                        "content": f"The Python code was giving an error {err_message}. \
                                   The traceback is as below:\n{result}\nPlease correct your Python code.",
                    }
                )
            elif result:
                # Assume Python code output has desired result to feed back to master LLM
                data_extracted = result
            else:
                # No data extracted
                llm_prompt.append(
                    {"role": "assistant", "content": "Please re-write the Python code as no valid data was extracted"}
                )

        if data_extracted != -1:
            return data_extracted
        else:
            return "UN_RELEVANT"

    def invoke_llm_for_csv_content(self, csv_content, filtering_keywords, extraction_keys, instruction_str, chat_id):
        instruction_file = "llm_prompt_info_extraction_csv.txt"  # Instructions for processing CSV as full
        with open("./instructions/" + instruction_file, "r", encoding="utf-8") as file:
            instructions = file.read()

        instructions = instructions.replace("<<filtering_keys>>", str(filtering_keywords))
        instructions = instructions.replace("<<extraction_keys>>", str(extraction_keys))
        instructions = instructions.replace("<<user_question>>", str(instruction_str))

        # Generate a python code to extract data from this CSV file from LLM
        # This is executed in a loop until the data is properly output by code, maximum 2 attempts
        llm_prompt = []
        llm_prompt.append({"role": "system", "content": instructions})
        llm_prompt.append(
            {
                "role": "user",
                "content": f"Please extract the relevant data for user question from following CSV content.\n",
            }
        )
        llm_prompt.append({"role": "user", "content": str(csv_content)})
        response = None
        try:
            response = self.llm_client.get_llm_response(
                self,
                None,
                None,
                message_list=llm_prompt,
                model=self.llm_name_unstructured_processing,
                seed=1234,
                token_limit=4096,
            )
        except Exception as e:
            print(e)
            return None

        # count tokens
        self.update_token_count(response, chat_id)
        llm_output = response.choices[0].message.content
        return llm_output

    """
    Check whether given document is relevant for answering user question
    Returns True if relevant  and False otherwise

    """

    def is_doc_relevant(self, document_chunks_dict, filtering_keywords, instruction_str, chat_id, chat_log):
        filter_instruction = ""
        with open("./instructions/subllm_document_filter_instruct.txt", encoding="utf-8") as file1:
            filter_instruction = file1.read()
        filter_instruction = filter_instruction.replace("<<filtering_keys>>", str(filtering_keywords))
        filter_instruction = filter_instruction.replace("<<user_question>>", str(instruction_str))

        filter_llm_prompt = []
        filter_llm_prompt.append({"role": "system", "content": filter_instruction})
        filter_llm_prompt.append(
            {"role": "user", "content": f"Is this document relevant to above question? {document_chunks_dict}"}
        )
        try:
            chat_log.debug(f"FILTER LLM INPUT:\n{filter_llm_prompt}")

            relevance_response = self.llm_client.get_llm_response(
                self,
                None,
                chat_log,
                message_list=filter_llm_prompt,
                model=self.llm_name_unstructured_processing,
                seed=1234,
                token_limit=4096,
            )
            self.update_token_count(relevance_response, chat_id)  # count tokens
            llm_filter_output = relevance_response.choices[0].message.content
            chat_log.debug(f"FILTER LLM OUTPUT:\n{llm_filter_output}")
            if check_unrelevent(llm_filter_output):
                chat_log.debug("CSV file not relevant")
                return False
            else:
                return True

        except Exception as e:
            chat_log.debug(f"Error calling sub LLM filter: {e}")
            return False

    def update_token_count(self, llm_response, chat_id):
        conversation_collection = MongoDBmanager("Conversations")

        update_quary = {
            "prompt_tokens": llm_response.usage.prompt_tokens,
            "completion_tokens": llm_response.usage.completion_tokens,
            "total_tokens": llm_response.usage.total_tokens,
        }

        conversation_collection.find_one_update_inc(
            {
                "_id": ObjectId(chat_id),
            },
            update_quary,
            [],
        )

    def get_conversation_obj_by_id(self, chat_id):
        # Access conversation from global memory
        try:
            if str(chat_id) in self.global_memory.active_conversations:
                conversation_obj = self.global_memory.active_conversations[str(chat_id)]
                return conversation_obj
            return None
        except Exception as e:
            logger.warning(f"Failed to get conversation object by id {chat_id}: {e}")
            logger.warning(traceback.format_exc())
            return None

    def get_initial_question_from_conversation(self, chat_id):
        # Access conversation from global memory and retrieve the initial question
        conversation_obj = self.get_conversation_obj_by_id(chat_id)
        if conversation_obj:
            question = conversation_obj.initial_question
            return str(question)
        else:
            return ""

    def load_data_dictionary_from_json(self):
        """
        Loads the _data variable directly from the data_dict.json file.
        Returns the data dictionary or None if the operation fails.
        """
        data_dict_json_path = "./data_dictionaries/data_dict.json"

        if os.path.exists(data_dict_json_path):
            try:
                with open(data_dict_json_path, "r", encoding="utf-8") as json_file:
                    _data = json.load(json_file)  # Load the JSON _data directly into _data
                logger.info(
                    f"Data Dict Configuration | MetaLake.load_data_dictionary_from_json | N/A | Data dictionary loaded successfully from {data_dict_json_path}"
                )
                return _data  # Return _data directly
            except Exception as e:
                logger.error(
                    f"Data Dict Configuration | MetaLake.load_data_dictionary_from_json | N/A | Error loading data dictionary from {data_dict_json_path}: {e}"
                )
                return None
        else:
            logger.warning(
                f"Data Dict Configuration | MetaLake.load_data_dictionary_from_json | N/A | {data_dict_json_path} does not exist."
            )
            return None

    def retrieve_db_unstructured_records(self, categories, filter_key_values, page_index):
        requested_keys = list(filter_key_values.keys())
        data_dictionary = self.load_data_dictionary_from_json()
        if data_dictionary is not None:
            db_unstructured_sections = [d for d in data_dictionary if d["type"] == "db-unstructured"]

        logger.debug("db_unstructured_sections in metalake : ", db_unstructured_sections)
        available_keys = list({key for entry in db_unstructured_sections for key in entry["filter_keys"]})
        logger.debug("available_keys in metalake retrieve_db_unstructured_records : ", available_keys)
        for requested_key in requested_keys:
            if requested_key not in available_keys:
                raise Exception(f"filter key {requested_key} not found. Available filter keys are: {available_keys}")

        return self.layernext_client.retrieve_db_unstructured_records(categories, filter_key_values, page_index)

    """
    def get_projected_data(self, goal_subject: str, goal_value: float, goal_period: int):
        #Check if there is matching function for subject
        functions_map = FUNC[str(self.company)]["goal_projection_functions"] #TODO: remove hard coding of PDRMax
        projection_function = None
        if goal_subject in functions_map:
            projection_function = functions_map[goal_subject]
        if projection_function:
            #Load the function module dynamically
            #import_module(f"data_dictionaries.{self.company}.business_functions.{projection_function}")    #TODO: fix dynamic import
            value_list = projection_function(goal_subject, goal_value, goal_period)
            #If projection function failed then use the default one
            if value_list:
                return value_list
        
        #Default method: called if there is no matched projection function or it has failed
        step_goal = goal_value / goal_period
        # Calculate cumulative value at the end of each step
        cumulative_value_list = [step_goal * (month + 1) for month in range(goal_period)]
        return cumulative_value_list
    """
    """

    def get_goal_tracking_data(
        self, goal_subject: str, goal_unit: str, goal_value: float, goal_period: int, start_value: float = 0
    ):
        # Check if there is matching function for subject
        functions_map = FUNC[str(self.company)]["goal_projection_functions"]
        is_handle_with_default = True
        value_list = []
        projection_function = None
        if goal_subject in functions_map:
            projection_function = functions_map[goal_subject]
        if projection_function:
            # Load the function module dynamically
            # import_module(f"data_dictionaries.{self.company}.business_functions.{projection_function}")    #TODO: fix dynamic import
            value_list = projection_function(goal_subject, goal_value, goal_period)
            # If projection function failed then use the default one
            if value_list:
                is_handle_with_default = False

        if is_handle_with_default:
            # Default method: called if there is no matched projection function or it has failed
            step_goal = (goal_value - start_value) / goal_period
            value_list = [step_goal * (step + 1) for step in range(goal_period - start_value)]

        # Return in markdown table format
        unit_str = f"({goal_unit}) " if goal_unit else ""
        output_str = (
            f"| Date       | Goal projected {goal_subject} {unit_str}|\n|------|-----------------------------|"
        )
        for i, value in enumerate(value_list):
            if i > goal_period:
                break
            output_str += f"| {i} | {value} |\n"

        return output_str
    
    def get_combine_actual_and_forecasted(
        self,
        goal_subject: str,
        goal_unit: str,
        actual_data: list,
        forecasted_data: list,
        start_of_period: datetime,
        end_of_period: datetime,
        is_cumulative: bool = True,
    ):
        import pandas as pd

        # Creating a DataFrame for actual revenue
        df_actual = pd.DataFrame(actual_data)
        actual_date_column_type = str(df_actual["date"].dtype)
        # if 'datetime' not in actual_date_column_type:
        df_actual["date"] = pd.to_datetime(df_actual["date"])

        # Creating a DataFrame for forecasted revenue
        df_forecasted = pd.DataFrame(forecasted_data)
        forecast_date_column_type = str(df_forecasted["date"].dtype)
        # if 'datetime' not in forecast_date_column_type:
        df_forecasted["date"] = pd.to_datetime(df_forecasted["date"])

        # Creating a date range for the current month
        date_range = pd.date_range(
            start=start_of_period.replace(hour=0, minute=0, second=0, microsecond=0),
            end=end_of_period.replace(hour=0, minute=0, second=0, microsecond=0),
        )

        # Merging actual and forecasted data
        combined_df = pd.concat([df_actual, df_forecasted], ignore_index=True)

        # Ensuring all dates are present
        all_dates_df = pd.DataFrame(date_range, columns=["date"])
        all_dates_df = all_dates_df.merge(combined_df, on="date", how="left")

        # Filling missing values with 0
        all_dates_df["count"] = all_dates_df["count"].fillna(0)

        # Calculate the cumulative value if required
        if is_cumulative:
            all_dates_df["count"] = all_dates_df["count"].cumsum()

        # Formatting the output
        df_output = all_dates_df[["date", "count"]]
        df_output["date"] = df_output["date"].dt.day

        
        # Printing the table
        unit_str = f"({goal_unit}) " if goal_unit else ""
        output_str = (
            f"| Date       | Actual and Forecasted {goal_subject} {unit_str}|\n|------------|--------------------|"
        )
        for _, row in df_output.iterrows():
            output_str += f"\n| {row['date']} | {row['count']:.2f} |"
        return output_str
    """

    def retrieve_documents(self, document_categories, document_search_keys, question, page_index):
        doc_dict = {}
        if page_index == 0:
            doc_dict["unique_name"] = "file_name.pdf"
            return [doc_dict]
        else:
            return []

    def get_input_data(self, chat_id):
        semi_structured_data = self.global_memory.semi_structured_db_unstructured_data.get(str(chat_id), {}).get(
            "merged_result", {}
        )
        return semi_structured_data

    def test_query_unstructured_data_by_label(
        self, data_fetch_query, label_list, data_source_name, table_name, column_name
    ):
        unstructured_data_list = run_sql_query(data_source_name, data_fetch_query, logger)
        self.unstructured_data_handler.test_query_unstructured_data_by_label(
            unstructured_data_list, label_list, data_source_name, table_name, column_name
        )


meta = MetaLake()


def locate_document_and_retrieve_data_elements(document_categories, document_keywords, question, chat_id, page_index):
    """
    Retrieves matching documents and matching elements in each of the document based on specified parameters.

    Parameters:
      1. document_categories (list): Category names relevant to each section of data being considered for locating documents.
      2. document_search_keys (list): Keywords (strings) derived from the question to locate matching documents.
      3. question (str): User question to identify the relevant document elements.
      4. page_index (int): Index for pagination of results.

    Returns:
      a list of dictionaries, where each dictionary contains relevant document elements and other metadata for a single document as shown below.
        [
          {
            "title": "Title_1",
            "unique_name":"File1.pdf",
            "file_type": "pdf",
            "elements": [element_1, element_2]
          }, {
            "title": "Title_2",
            "unique_name":"File2.csv",
            "file_type": "csv",
            "elements": [element_1]
          }
        ]
    """
    # Load default page size from env. variable DOCUMENT_PROCESS_PAGE_SIZE
    page_size = int(os.getenv("DOCUMENT_PROCESS_PAGE_SIZE", 10))
    original_question = ""
    # Get original question from chat id
    if chat_id:
        original_question = meta.get_initial_question_from_conversation(chat_id)
        if not original_question:
            original_question = question
        return meta.layernext_client.query_documents_with_structure(
            original_question, document_categories, document_keywords, question, chat_id, page_index, page_size
        )


def extract_document_data(document_key: str, user_question: str, extraction_keys, chat_id: str):
    """
    Extracts data elements from a document based on specified parameters.

    Parameters:
      1. document_key (str): Unique name of the document to be extracted.
      2. user_question (str): User question to identify the relevant document elements.
      3. extraction_keys (list): Keys to be extracted from the document.
      4. chat_id (str): Conversation identifier.
    """
    chat_log = get_debug_logger(f"chat_{chat_id}", f"./logs/chat_{chat_id}.log")
    # Step1: Get the conversation from chat id
    conversation_obj: Conversation = meta.get_conversation_obj_by_id(chat_id)
    if not conversation_obj:
        chat_log.warning(f"extract_document_data: Failed to get conversation object by id {chat_id}")
        return -1

    # Step2: Retrieve the elements list of requested document saved in the DB against the conversation
    doc_elements_dict = conversation_obj.get_doc_element_meta_info(document_key, logger)
    if not doc_elements_dict:
        chat_log.warning(f"extract_document_data: Failed to get document elements for document {document_key}")
        return -1

    # Step3: Call the extraction SDK function (extract_document_elements_keyInfo) with the elements list
    return meta.layernext_client.extract_document_elements_keyInfo(
        doc_elements_dict, extraction_keys, user_question, chat_id
    )


def load_from_csv(chat_id: str, csv_file_name: str):
    """
    Utility function to load the data as a pandas dataframe from given csv file
    The CSV file should be located in the path "storage/public/<chat_id>/files"
    Parameters:
      1. chat_id (str): Conversation identifier.
      2. csv_file_name (str): Name of the CSV file to be loaded
    Returns:
      a pandas dataframe if the file is loaded successfully, None otherwise
    """
    import pandas as pd

    try:
        return pd.read_csv(f"storage/public/{chat_id}/files/{csv_file_name}")
    except:
        return None


def get_estimated_processing_time(doc_elements_list: list):
    """
    Returns an estimated processing time for the given list of document elements.

    Parameters:
      1. doc_elements_list (list): List of document elements to be processed.
        [
          {
            "title": "Title_1",
            "unique_name":"File1.pdf",
            "file_type": "pdf",
            "elements": [element_1, element_2]
          }, {
            "title": "Title_2",
            "unique_name":"File2.csv",
            "file_type": "csv",
            "elements": [element_1]
          }
        ]
    Returns:
      string: Estimated processing time in minutes and seconds.
    """

    def convert_seconds_to_min_sec(seconds):
        minutes, seconds = divmod(seconds, 60)
        second_str = f"{seconds} second{'s' if seconds != 1 else ''}"

        if minutes == 0:
            return second_str
        else:
            minute_str = f"{minutes} minute{'s' if minutes != 1 else ''}"
            return f"{minute_str} {second_str}"

    # Get estimate based on the no of elements, 1 element = 5 seconds + 10 seconds to write python code
    est_time = 10
    for doc in doc_elements_list:
        est_time += len(doc["elements"]) * 5
    return convert_seconds_to_min_sec(est_time)


def remove_trailing_semicolon(query: str) -> str:
    """
    Removes trailing semicolon from SQL query while preserving any content after it.

    Parameters:
        query (str): The SQL query string to process

    Returns:
        str: SQL query with trailing semicolon removed
    """
    semicolon_index = query.rfind(";")
    if semicolon_index < 0:
        return query
    substring_after_semicolon = query[semicolon_index + 1 :]
    if "\n" in substring_after_semicolon or " " in substring_after_semicolon:
        return query[:semicolon_index] + substring_after_semicolon
    else:
        return query[:semicolon_index]


def run_sql_query(source_name: str, query: str, is_via_single_code=True):
    # If this is not called via single code, then don't need to check the record count
    if not is_via_single_code:
        return meta.layernext_client.run_sql_query(source_name, query)

    # If we find instances where "DATA_SOURCE.TABLE_NAME" in the SQL, then replace "DATA_SOURCE." with empty string
    query = query.replace(f"{source_name}.", "")

    # Get the data source type from the data dictionary
    data_dict_handler = DataDictionaryService()
    source_type = data_dict_handler.get_data_source_type(source_name)

    # Separate routing for mssql as we can't get the record count by adding count(*) in the query in case of mssql
    if source_type == "mssql" or source_type == "unknown":
        # Replace "SELECT" with "SELECT TOP 5000" if the SQL doesn't contain TOP, OFFSET, DISTINCT or COUNT
        original_query = query
        if (
            "TOP" not in query.upper()
            and "OFFSET" not in query.upper()
            and "DISTINCT" not in query.upper()
            and "COUNT(" not in query.upper()
        ):
            query = query.replace("SELECT", "SELECT TOP 5000")
        # Execute query and check the length of result set
        initial_records_list = meta.layernext_client.run_sql_query(source_name, query)
        # If the length is less than 5000, then return but otherwise do batch-wise fetch for remaining records
        if len(initial_records_list) < 5000:
            return initial_records_list
        else:
            # Remove trailing semicolon if any
            query = remove_trailing_semicolon(original_query)
            offset_len = 5000
            all_records = initial_records_list
            while True:
                # Add order by clause if not available
                if "ORDER BY" not in query.upper():
                    query = query + " ORDER BY 1"
                query_with_offset = f"{query} OFFSET {offset_len} ROWS FETCH NEXT 5000 ROWS ONLY;"
                records_batch = meta.layernext_client.run_sql_query(source_name, query_with_offset)
                if not records_batch:
                    break
                all_records.extend(records_batch)
                offset_len += 5000
            return all_records

    # remove tailing semicolon if any (including before space, newline, etc.)
    query = remove_trailing_semicolon(query)

    # Check the returning row count before executing the query
    # If the SQL is already a count or distinct query, then skip this step
    if "COUNT(" in query.upper() or "DISTINCT" in query.upper():
        records_list = meta.layernext_client.run_sql_query(source_name, query)
        return records_list

    count_query = "SELECT COUNT(*) AS ROW_COUNT FROM ( " + query + " ) AS COUNT_SUBQUERY;"
    count_result = meta.layernext_client.run_sql_query(source_name, count_query)
    record_count = count_result[0]["ROW_COUNT"]
    # If record count is less than 5000, just execute the query
    # If it's more than 100,000, then return error asking to use aggregate and grouping
    # If record count is more than 5000, then fetch results batch wise
    if record_count < 5000:
        records_list = meta.layernext_client.run_sql_query(source_name, query)
        return records_list
    elif record_count > 100000:
        return "Error: Too many records to fetch. Please use aggregate and grouping functions instead of fetching all records."
    else:
        # Determine the number of 5000 batches needed
        num_batches = int((record_count + 4999) / 5000)  # Adding 499 to round up to the nearest 5000
        all_records = []
        for i in range(num_batches):
            offset = i * 5000
            query_with_offset = f"SELECT * FROM ( {query} ) AS DERIVED_TABLE LIMIT 5000 OFFSET {offset};"
            records_batch = meta.layernext_client.run_sql_query(source_name, query_with_offset)
            all_records.extend(records_batch)
        return all_records


def add_knowledge_blocks(data: dict):
    """
    Adds knowledge blocks to metalake

    @data: dict {
        "knowledge_blocks": [],
        "knowledge_tree": {}
    }
    """
    return meta.layernext_client.add_knowledge_blocks(data)


def get_result_count_from_sql(source_name: str, query: str):
    """
    Returns the result count from the given SQL query
    """
    count_query = "SELECT COUNT(*) AS ROW_COUNT FROM ( " + query + " ) AS COUNT_SUBQUERY;"
    count_result = meta.layernext_client.run_sql_query(source_name, count_query)
    record_count = count_result[0]["ROW_COUNT"]
    return record_count


def get_top_rows_from_sql(source_name: str, query: str, count: int):
    all_rows = run_sql_query(source_name, query, False)
    if all_rows is None:
        return []
    return all_rows[:count]


def save_data(chat_id: str, data_file_name: str, data_rows: list):
    """
    Save all the retrieved data to a disk file - to be accessed by python code generation agent through fetch_data function
    Parameters:
        data_rows: List of dictionaries containing the data
        reference_name: Name of the reference to identify the data
    """
    # If the file is .csv, then save it as CSV format additionally
    if data_file_name.endswith(".csv"):
        import pandas as pd

        df = pd.DataFrame(data_rows)
        df.to_csv(f"storage/public/{chat_id}/{data_file_name}", index=False)
        # Change the file name to .dat
        data_file_name = data_file_name.replace(".csv", ".dat")
    # Use InMemoryDataManager to save the data
    data_manager = InMemoryDataManager()
    return data_manager.save(data_rows, chat_id, data_file_name)


def load_data(chat_id: str, data_file_name: str):
    """
    Fetches the data records saved in memory disk file (pickle) and returns a pandas data frame
    Parameters:
        data_file_name: Name of the reference to identify the data
        chat_id: Conversation ID
    Returns:
        Pandas data frame containing the data - empty data frame if no data found
    """
    import pandas as pd

    # If this is .csv file then load from csv file instead of pickle
    if data_file_name.endswith(".csv"):
        return load_from_csv(chat_id, data_file_name)
    # Use InMemoryDataManager to load the data from .dat file
    elif data_file_name.endswith(".dat"):
        data_manager = InMemoryDataManager()
        data_rows = data_manager.load(chat_id, data_file_name)
        return pd.DataFrame(data_rows)
    else:
        return pd.DataFrame([])


def cognitive_list_match(
    chat_id: str, reference_df: pd.DataFrame, target_df: pd.DataFrame, column_list: list, match_criteria: str
):
    """
    Perform a cognitive match on the target DataFrame for the reference DataFrame based on given match criteria and returns the matching rows as a new DataFrame.
    """
    return meta.unstructured_data_handler.cognitive_list_match(
        chat_id, reference_df, target_df, column_list, match_criteria
    )


def intelligent_text_match_single_column(
    chat_id: str, input_df: pd.DataFrame, column_name: str, match_object: dict, match_criteria: str
):
    """
    Perform a semantic match on the specified column of the DataFrame `input_df` for the given object based on given match criteria and returns the matching rows as a new DataFrame.
    """
    chat_log = get_debug_logger(f"chat_{chat_id}", f"./logs/chat_{chat_id}.log")
    return meta.unstructured_data_handler.semantic_search(
        input_df, [column_name], match_object, match_criteria, chat_id, chat_log
    )


def intelligent_text_match(
    chat_id: str, input_df: pd.DataFrame, column_names: list, match_object: dict, match_criteria: str
):
    """
    Perform a semantic match on the specified list of columns of the DataFrame `input_df` for the given object based on given match criteria and returns the matching rows as a new DataFrame.
    """
    chat_log = get_debug_logger(f"chat_{chat_id}", f"./logs/chat_{chat_id}.log")
    return meta.unstructured_data_handler.semantic_search(
        input_df, column_names, match_object, match_criteria, chat_id, chat_log
    )


def update_excel_sheet(
    ws: openpyxl.worksheet.worksheet.Worksheet,
    df: pd.DataFrame,
    key_map: list[tuple[str, str]],
    update_map: list[tuple[str, str]],
    header_row: int = 1,
    start_row: int = None,
):
    """
    Update specified columns in an Excel sheet from a DataFrame, matching on key columns.
    Parameters:
        ws: openpyxl worksheet
        df: pandas DataFrame containing the data to update from
        key_map: List of tuples containing the Excel column letter and the DataFrame column name to match on
        update_map: List of tuples containing the Excel column letter and the DataFrame column name to update
        header_row: Row number containing the column headers
        start_row: First row containing data (defaults to header_row + 1)
    Returns:
        {
            "rows_scanned": int,
            "cells_updated": int,
            "rows_matched": int,
            "rows_not_found": list[int]      # Excel row numbers that had no match
        }
    """

    return update_excel_sheet_from_df(ws, df, key_map, update_map, header_row, start_row)


def quickbooks_create_bill(bill_data_object: dict):
    """
    Create a bill in QuickBooks
    """
    bill_create_res = meta.layernext_client.quickbooks_create_or_update_object("createBill", bill_data_object)
    return bill_create_res


def quickbooks_update_bill(bill_data_object: dict):
    """
    Update a bill in QuickBooks
    """
    bill_update_res = meta.layernext_client.quickbooks_create_or_update_object("updateBill", bill_data_object)
    return bill_update_res


def quickbooks_create_vendor(vendor_data_object: dict):
    """
    Create a vendor in QuickBooks
    """
    vendor_create_res = meta.layernext_client.quickbooks_create_or_update_object("createVendor", vendor_data_object)
    return vendor_create_res


def quickbooks_update_vendor(vendor_data_object: dict):
    """
    Update a vendor in QuickBooks
    """
    vendor_update_res = meta.layernext_client.quickbooks_create_or_update_object("updateVendor", vendor_data_object)
    return vendor_update_res


def quickbooks_create_bill_payment(bill_payment_data_object: dict):
    """
    Create a bill payment in QuickBooks
    """
    bill_payment_create_res = meta.layernext_client.quickbooks_create_or_update_object(
        "createBillPayment", bill_payment_data_object
    )
    return bill_payment_create_res


def quickbooks_update_bill_payment(bill_payment_data_object: dict):
    """
    Update a bill payment in QuickBooks
    """
    bill_payment_update_res = meta.layernext_client.quickbooks_create_or_update_object(
        "updateBillPayment", bill_payment_data_object
    )
    return bill_payment_update_res


def quickbooks_create_purchase(purchase_data_object: dict):
    """
    Create a purchase in QuickBooks
    """
    purchase_create_res = meta.layernext_client.quickbooks_create_or_update_object(
        "createPurchase", purchase_data_object
    )
    return purchase_create_res


def quickbooks_update_purchase(purchase_data_object: dict):
    """
    Update a purchase in QuickBooks
    """
    purchase_update_res = meta.layernext_client.quickbooks_create_or_update_object(
        "updatePurchase", purchase_data_object
    )
    return purchase_update_res


def quickbooks_upload_attachment(chat_id: str, file_name: str, object_type: str, object_id: str):
    """
    Upload attachment to QuickBooks object
    """
    return meta.layernext_client.quickbooks_upload_attachment(chat_id, file_name, object_type, object_id)


def quickbooks_query_object(object_type: str, query: str):
    """
    Query QuickBooks object
    """
    return meta.layernext_client.quickbooks_query_object(object_type, query)


def quickbooks_get_company_currency():
    """
    Get QuickBooks preferences for the company and return the currency code
    """
    preferences_info = meta.layernext_client.quickbooks_get_object("Preferences", "")
    if preferences_info and "data" in preferences_info and "CurrencyPrefs" in preferences_info["data"]:
        home_currency = preferences_info["data"]["CurrencyPrefs"].get("HomeCurrency", "")
        return home_currency["value"] if home_currency else ""
    return ""


# locate_document_and_retrieve_data_elements = meta.layernext_client.query_documents_with_structure
retrieve_data_elements_by_unique_names = meta.layernext_client.get_documents_with_structure
run_mongodb_aggregation = meta.layernext_client.run_mongodb_aggregation

insert_mongodb_data = meta.layernext_client.insert_mongodb_data
delete_mongodb_data = meta.layernext_client.delete_mongodb_data

retrieve_text_chunks = meta.layernext_client.retrieve_text_chunks
# extract_document_keyInfo_llm = meta.extract_document_keyInfo_llm
retrieve_db_unstructured_records = meta.retrieve_db_unstructured_records
process_unstructured_data = meta.process_unstructured_data
# conclude_extracted_data = meta.conclude_extracted_data
# get_goal_tracking_data = meta.get_goal_tracking_data
# get_combine_actual_and_forecasted = meta.get_combine_actual_and_forecasted
extract_document_elements_keyInfo = meta.layernext_client.extract_document_elements_keyInfo
get_file_details = meta.layernext_client.get_file_details
get_table_schema = meta.layernext_client.get_table_schema
get_input_data = meta.get_input_data
fill_unstructured_data_cache = meta.fill_unstructured_data_cache
# llm_review_unstructured_data_labels = meta.llm_review_unstructured_data_labels
test_query_unstructured_data_by_label = meta.test_query_unstructured_data_by_label
