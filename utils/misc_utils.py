"""
 * Copyright (c) 2024 LayerNext Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
Miscellaneous utility functions
"""

import math
import platform
import traceback
from typing import List
from bson import ObjectId
from datetime import datetime, timedelta
import requests
import os
import xml.etree.ElementTree as ET

# from currency_converter import CurrencyConverter

from utils.constant import DataBlockVisibility, UserType


def remove_fields_from_dict(data, fields_to_remove):
    """
    Recursively remove specified fields from a dictionary or list of dictionaries.

    Args:
        data (dict or list): Input dictionary or list of dictionaries.
        fields_to_remove (list): List of field names to remove.

    Returns:
        dict or list: Cleaned dictionary or list of dictionaries.
    """
    if isinstance(data, dict):
        # Remove specified fields from dictionary
        for field in fields_to_remove:
            if field in data:
                del data[field]

        # Recursively check nested dictionaries or lists
        for key, value in list(data.items()):
            if isinstance(value, (dict, list)):
                data[key] = remove_fields_from_dict(value, fields_to_remove)

    elif isinstance(data, list):
        # Apply the cleaning function to each element in the list
        for index, item in enumerate(data):
            if isinstance(item, (dict, list)):
                data[index] = remove_fields_from_dict(item, fields_to_remove)

    return data


def make_json_compatible(data):
    """
    Recursively makes data JSON-compatible by:
    - Converting MongoDB ObjectId to string
    - Replacing NaN, Infinity, and -Infinity with None
    """
    if isinstance(data, ObjectId):  # Handle MongoDB ObjectId
        return str(data)
    elif isinstance(data, float):  # Handle invalid floats
        if math.isinf(data) or math.isnan(data):
            return None  # Replace with None or any other placeholder
        return data
    elif isinstance(data, list):  # Handle lists
        return [make_json_compatible(item) for item in data]
    elif isinstance(data, dict):  # Handle dictionaries
        return {key: make_json_compatible(value) for key, value in data.items()}
    return data  # Return other data types unchanged


def get_table_headers_and_rows_from_csv(csv_file_name, chat_id, chat_log):
    import pandas as pd

    csv_path = f"./storage/public/{chat_id}/files/{csv_file_name}"
    try:
        df = pd.read_csv(csv_path)
        headers = list(df.columns)
        rows = df.values.tolist()
        return headers, rows
    except Exception as e:
        chat_log.info(f"Error reading CSV file {csv_path}: {e}")
        return [], []


def csv_to_markdown(file_path, max_rows):
    """
    Convert a CSV file to a markdown string.
    Note: If the no. of records goes over (Max rows), the data is truncated.
    Args:
    - file_path (str): The path to the CSV file to convert.
    - max_rows (int): The maximum number of rows to include in the markdown string.

    Returns:
    - (str, bool, int): The markdown string representation of the CSV file, in case of error, returns empty string + boolean flag indicating whether data was truncated or not, and row count

    """
    import pandas as pd

    row_count = 0
    try:
        # Load the CSV file into a DataFrame
        df = pd.read_csv(file_path)
        row_count = df.shape[0]
        # Check row count
        if df.shape[0] > max_rows:
            df = df.head(max_rows)
            truncated = True
        else:
            truncated = False
        # Substitute '|' with HTML entity &#124; within data frame to avoid markdown parsing issues
        df = df.applymap(lambda x: x.replace("|", "&#124;") if isinstance(x, str) else x)
        # Convert DataFrame to markdown format
        markdown_string = df.to_markdown(index=False)
        markdown_string = markdown_string.encode("utf-8").decode("unicode_escape")
        return markdown_string, truncated, row_count
    except:
        return "", False, row_count


def list_to_markdown(dict_list: list):
    """
    Convert a list of dictionaries to a markdown table.
    Args:
    - dict_list (list): The list of dictionaries to convert.

    Returns:
    - str: The markdown table representation of the list of dictionaries.
    """
    import pandas as pd

    if not dict_list:
        return ""
    df = pd.DataFrame(dict_list)
    # Substitute '|' with HTML entity &#124; within data frame to avoid markdown parsing issues
    df = df.applymap(lambda x: x.replace("|", "&#124;") if isinstance(x, str) else x)
    str_table = df.to_markdown(index=False)
    return str_table


def batch_generator(data_list, batch_size=20):
    batches = []
    for i in range(0, len(data_list), batch_size):
        batches.append(data_list[i : i + batch_size])
    return batches


def get_month_number(month_name):
    try:
        # Convert input to lowercase
        month_name = month_name.lower()
        # Get index (0-11) and add 1 to get month number (1-12)
        return [
            "january",
            "february",
            "march",
            "april",
            "may",
            "june",
            "july",
            "august",
            "september",
            "october",
            "november",
            "december",
        ].index(month_name) + 1
    except ValueError:
        return -1


def format_number(value):
    """Format a number into human-readable string with K, M, B abbreviations"""
    if value is None:
        return None

    # Convert to float and handle very small/large numbers
    value = float(value)
    abs_value = abs(value)

    if abs_value >= 1_000_000_000:
        formatted = f"{value/1_000_000_000:.2f}B"
    elif abs_value >= 1_000_000:
        formatted = f"{value/1_000_000:.2f}M"
    elif abs_value >= 1_000:
        formatted = f"{value/1_000:.2f}K"
    else:
        formatted = f"{value:.2f}"

    # Remove trailing zeros and decimal point if unnecessary
    formatted = formatted.rstrip("0").rstrip(".") if "." in formatted else formatted
    return formatted


def format_human_readable_date(dt: datetime) -> str:
    if not isinstance(dt, datetime):
        return ""

    try:
        day_format = "%-d" if platform.system() != "Windows" else "%#d"
        return dt.strftime(f"%B {day_format}, %Y")
    except Exception:
        return ""


def get_data_block_visibility_list(user_type: str) -> List[DataBlockVisibility]:
    if user_type == UserType.USER_TYPE_SUPER_ADMIN.value:
        return [DataBlockVisibility.ALL_USERS.value, DataBlockVisibility.SUPER_ADMIN_ONLY.value]
    else:
        return [DataBlockVisibility.ALL_USERS.value, DataBlockVisibility.ALL_EXCLUDE_SUPER_ADMIN.value]


def generate_csv_from_records(chat_id: str, file_name: str, records, columns: list[str] = []):
    import pandas as pd
    import os
    from utils.metalake import save_data

    # If records is not a list of dictionaries, convert it
    if not isinstance(records, list):
        records = [records]
    try:
        if columns:
            # records format: [[key1, val1], [key2, val2], ...]]
            # Convert this to list of dicts
            records = [dict(zip(columns, record)) for record in records]
        else:
            columns = records[0].keys()

        # Check the fields in each record, if new line character is there, replace it with space in order to preserve CSV format
        for record in records:
            for key in record:
                if isinstance(record[key], str):
                    record[key] = record[key].replace("\n", " ")

        file_path = f"./storage/public/{chat_id}/files/{file_name}"
        # Create folder if not exists
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        df = pd.DataFrame(records, columns=columns)
        df.to_csv(file_path, index=False)
        # Markdown table preview (first 10 rows)
        # Substitute '|' with HTML entity &#124; within data frame to avoid markdown parsing issues
        df = df.applymap(lambda x: x.replace("|", "&#124;") if isinstance(x, str) else x)
        markdown_preview = df.head(10).to_markdown(index=False)
        # Save data file
        dat_file_name = file_name.replace(".csv", ".dat")
        save_data(chat_id, dat_file_name, records)
        return file_path, markdown_preview
    except Exception as e:
        return "", f"Error generating CSV: {e}: {traceback.format_exc()}"


# Currency Rate Management

# Update ECB XML data


def _extract_ecb_date(xml_bytes: bytes) -> datetime:
    # Extracts the latest date from ECB XML (eurofxref XML format).
    try:
        root = ET.fromstring(xml_bytes)
        namespace = {"ns": "http://www.ecb.int/vocabulary/2002-08-01/eurofxref"}
        cube_time = root.find(".//ns:Cube/ns:Cube", namespace)
        if cube_time is None:
            # fallback to no-namespace format
            cube_time = root.find(".//Cube/Cube")
        date_str = cube_time.attrib["time"]
        return datetime.strptime(date_str, "%Y-%m-%d").date()
    except Exception as e:
        raise ValueError(f"Could not parse ECB date: {e}")


def update_ecb_rates_xml(logger, save_path: str = "data/ecb_rates.xml") -> str:

    # Downloads the latest ECB daily exchange rate XML and saves it locally
    # if the local file is missing or outdated compared to today's date.

    # Parameters:
    #     save_path (str): Path to save the XML file.

    # Returns:
    #     str: Full path to the XML file (existing or updated).

    ECB_DAILY_URL = "https://www.ecb.europa.eu/stats/eurofxref/eurofxref-daily.xml"
    today_date = datetime.today().date()

    try:
        # Check if local file is already up to date
        if os.path.exists(save_path):
            with open(save_path, "rb") as f:
                local_xml = f.read()
            local_date = _extract_ecb_date(local_xml)

            if local_date >= today_date - timedelta(days=1):
                logger.debug(f"No update needed. Local ECB data is already for updated to ({local_date}).")
                return os.path.abspath(save_path)
            else:
                logger.info(f"Local ECB data is old ({local_date}). Fetching latest for today ({today_date})...")

        else:
            logger.info("No local ECB file found. Fetching fresh data...")

        # Download the remote file
        response = requests.get(ECB_DAILY_URL)
        response.raise_for_status()
        remote_xml = response.content
        remote_date = _extract_ecb_date(remote_xml)

        # Sanity check: warn if ECB data itself is outdated
        if remote_date < today_date - timedelta(days=1):
            logger.warning(f"Warning: ECB has not yet published updated data ({remote_date} < {today_date} - 1 day).")

        # Save new file
        with open(save_path, "wb") as f:
            f.write(remote_xml)

        logger.info(f"ECB rates updated to {remote_date} → {os.path.abspath(save_path)}")
        return os.path.abspath(save_path)

    except Exception as e:
        logger.error(f"Error while updating ECB rates: {e}: {traceback.format_exc()}")
        raise


import xml.etree.ElementTree as ET
from datetime import datetime


def _load_ecb_rates(xml_path: str) -> dict:
    """
    Loads ECB currency rates from XML and returns a dict {currency: rate_to_EUR}
    """
    tree = ET.parse(xml_path)
    root = tree.getroot()

    ns = {"e": "http://www.ecb.int/vocabulary/2002-08-01/eurofxref"}
    cube_time = root.find(".//e:Cube[@time]", ns) or root.find(".//Cube[@time]")
    if cube_time is None:
        raise ValueError("No date found in ECB XML.")

    rates = {"EUR": 1.0}  # base currency
    for cube in cube_time.findall("e:Cube", ns) or cube_time.findall("Cube"):
        currency = cube.attrib["currency"]
        rate = float(cube.attrib["rate"])
        rates[currency] = rate

    return rates


def convert_currency(amount: float, from_currency: str, to_currency: str, logger) -> float:
    """
    Converts amount from one currency to another using ECB rates.
    All conversions are done via EUR as the base.

    Example: convert_currency(100, 'USD', 'CAD')
    """
    if from_currency == to_currency:
        return {
            "is_success": True,
            "error_message": "",
            "converted_amount": amount,
            "conversion_rate": 1,
        }
    try:
        ecb_xml_path = update_ecb_rates_xml(logger)
        if not ecb_xml_path:
            return {
                "is_success": False,
                "error_message": "Failed to load ECB rates.",
                "converted_amount": 0,
                "conversion_rate": 0,
            }

        rates = _load_ecb_rates(ecb_xml_path)

        from_currency = from_currency.upper()
        to_currency = to_currency.upper()

        if from_currency not in rates:
            raise ValueError(f"Currency '{from_currency}' not found in ECB rates.")
        if to_currency not in rates:
            raise ValueError(f"Currency '{to_currency}' not found in ECB rates.")
        # Convert from → EUR → to
        amount_in_eur = amount / rates[from_currency]
        converted = amount_in_eur * rates[to_currency]
        conversion_rate = rates[to_currency] / rates[from_currency]
        logger.debug(f"Converted {amount} {from_currency} to {converted} {to_currency} at rate {conversion_rate}")
        return {
            "is_success": True,
            "error_message": "",
            "converted_amount": round(converted, 6),
            "conversion_rate": round(conversion_rate, 6),
        }
    except Exception as e:
        logger.error(f"Error while converting currency: {e}: {traceback.format_exc()}")
        return {
            "is_success": False,
            "error_message": f"Error converting currency: {e}",
            "converted_amount": 0,
            "conversion_rate": 0,
        }


# def convert_currency(amount: float, from_currency: str, to_currency: str, logger) -> dict[str, bool | str | float]:
#     """
#     Convert an amount from one currency to another using ECB exchange rates.
#     """
#     logger.info(f"Converting {amount} {from_currency} to {to_currency}...")
#     try:
#         """
#         # Load the ECB XML data
#         ecb_xml_path = update_ecb_rates_xml(logger)
#         if not ecb_xml_path:
#             return {
#                 "is_success": False,
#                 "error_message": "Failed to load ECB rates.",
#                 "converted_amount": 0,
#                 "conversion_rate": 0,
#             }

#         converter = CurrencyConverter(currency_file=ecb_xml_path)
#         """
#         converter = CurrencyRates()
#         # converted_amount = converter.convert(amount, from_currency, to_currency)
#         converted_amount = converter.convert(from_currency, to_currency, amount)
#         conversion_rate = converted_amount / amount
#         # conversion_rate = converter.get_rate(from_currency, to_currency)
#         return {
#             "is_success": True,
#             "error_message": "",
#             "converted_amount": converted_amount,
#             "conversion_rate": conversion_rate,
#         }
#     except Exception as e:
#         logger.error(f"Error while converting currency: {e}: {traceback.format_exc()}")
#         return {
#             "is_success": False,
#             "error_message": f"Error converting currency: {e}",
#             "converted_amount": 0,
#             "conversion_rate": 0,
#         }
