{"tableName": "purchase", "description": "The purchase table captures core details of each purchase transaction, including total amount, tax, payment type, credit status, and transaction date. It links to vendor, customer, account, address, and payment method entities, supporting both cash and credit purchases. Fields also include documentation references, internal notes, and timestamps for record creation, updates, and ETL syncs. This table underpins financial analysis, reconciliation, and integration with related entities, enabling accurate tracking of organizational purchase activities.", "fields": [{"name": "id", "dataType": "STRING", "description": "System-generated unique identifier for each purchase record, serving as the table's primary key.", "subsetOfAvailableValues": ["11", "12", "13", "15", "16", "and 40 more..."], "totalDistinctValueCount": 45, "is_unstructured": false}, {"name": "sync_token", "dataType": "STRING", "description": "Incremental value representing the current version of the record for concurrency and sync control.", "availableValues": ["0", "1", "2"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "credit", "dataType": "BOOL", "description": "Indicates if the purchase was made on credit (true) or not (false); typically used for purchases involving deferred payment.", "is_unstructured": false}, {"name": "doc_number", "dataType": "STRING", "description": "Reference code or document number used to track or reconcile the purchase, which may be shared across multiple purchases.", "is_unstructured": false}, {"name": "payment_type", "dataType": "STRING", "description": "Specifies the payment method category for the purchase, with allowed values: 'Check', 'Cash', or 'CreditCard'.", "availableValues": ["CreditCard", "Cash", "Check"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "private_note", "dataType": "STRING", "description": "Internal comment or note regarding the purchase, intended for private use and not guaranteed to be unique.", "availableValues": ["Opening Balance from Bank", "Service Charge INTERAC E-TRANSFER FEE"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "global_tax_calculation", "dataType": "STRING", "description": "Method of tax application on the purchase: 'NotApplicable' (no tax), 'TaxInclusive' (tax included in total), or 'TaxExcluded' (tax added to subtotal).", "availableValues": ["NotApplicable", "TaxExcluded", "TaxInclusive"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "created_at", "dataType": "TIMESTAMP", "description": "Timestamp marking when the purchase record was first entered into the system.", "subsetOfAvailableValues": ["2025-08-19 18:26:52+00:00", "2025-09-05 02:40:35+00:00", "2025-09-11 19:43:19+00:00", "2025-09-04 18:29:38+00:00", "2025-09-09 20:50:46+00:00", "and 39 more..."], "is_unstructured": false}, {"name": "updated_at", "dataType": "TIMESTAMP", "description": "Timestamp of the most recent modification to the purchase record, reflecting its last update.", "subsetOfAvailableValues": ["2025-08-19 18:26:52+00:00", "2025-09-05 02:40:37+00:00", "2025-09-11 19:43:21+00:00", "2025-09-04 18:29:38+00:00", "2025-09-09 20:50:48+00:00", "and 39 more..."], "is_unstructured": false}, {"name": "customer_id", "dataType": "STRING", "description": "Optional link to the customer associated with the purchase; foreign key to the customer table, rarely populated.", "is_unstructured": false}, {"name": "vendor_id", "dataType": "STRING", "description": "Foreign key referencing the vendor from whom the purchase was made; identifies the supplying party for the transaction.", "availableValues": ["7", "9", "10", "13", "14", "15", "16", "17", "18", "19", "20", "22", "23", "24", "25", "26", "27"], "totalDistinctValueCount": 17, "is_unstructured": false}, {"name": "remit_to_address_id", "dataType": "STRING", "description": "Foreign key pointing to the address designated for payment or correspondence related to the purchase, used in limited cases.", "is_unstructured": false}, {"name": "total_tax", "dataType": "BIGNUMERIC", "description": "Total calculated tax amount applied to the purchase, recorded as a high-precision numeric value.", "subsetOfAvailableValues": ["0", "11", "11.33", "0.84", "1.39", "and 7 more..."], "is_unstructured": false}, {"name": "total_amount", "dataType": "BIGNUMERIC", "description": "Grand total of the purchase, including all charges and taxes, stored as a high-precision numeric value.", "subsetOfAvailableValues": ["1", "231", "1100", "38941", "7.83", "and 12 more..."], "is_unstructured": false}, {"name": "transaction_date", "dataType": "DATE", "description": "Calendar date on which the purchase transaction took place, used for chronological tracking.", "subsetOfAvailableValues": ["2025-09-02", "2025-08-19", "2022-06-15", "2025-07-21", "2022-05-02", "and 9 more..."], "is_unstructured": false}, {"name": "account_id", "dataType": "STRING", "description": "Foreign key to the general ledger account that the purchase is recorded against, indicating its accounting classification.", "availableValues": ["54", "56", "57", "**********", "**********", "**********"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "payment_method_id", "dataType": "STRING", "description": "Optional foreign key referencing the specific payment method used for the purchase, such as a particular card or account.", "availableValues": ["1", "3", "4"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "_fivetran_synced", "dataType": "TIMESTAMP", "description": "Timestamp of the last synchronization for this record by Fivetran ETL, used for data integration audit purposes.", "subsetOfAvailableValues": ["2025-09-11 20:50:06.502000+00:00", "2025-09-04 22:07:44.719000+00:00", "2025-09-05 02:50:06.353000+00:00", "2025-09-08 14:49:35.424000+00:00", "2025-09-13 14:50:04.745000+00:00", "and 22 more..."], "is_unstructured": false}], "relationships": [{"child_table": "purchase", "parent_table": "department", "key_column_mapping": [{"parent_column": "id", "child_column": "department_id"}], "relationship_type": "many-to-one"}, {"child_table": "purchase", "parent_table": "currency", "key_column_mapping": [{"parent_column": "id", "child_column": "currency_id"}], "relationship_type": "many-to-one"}, {"child_table": "purchase", "parent_table": "account", "key_column_mapping": [{"parent_column": "id", "child_column": "account_id"}], "relationship_type": "many-to-one"}, {"child_table": "purchase", "parent_table": "address", "key_column_mapping": [{"parent_column": "id", "child_column": "remit_to_address_id"}], "relationship_type": "many-to-one"}, {"child_table": "purchase", "parent_table": "payment_method", "key_column_mapping": [{"parent_column": "id", "child_column": "payment_method_id"}], "relationship_type": "many-to-one"}, {"child_table": "purchase", "parent_table": "customer", "key_column_mapping": [{"parent_column": "id", "child_column": "customer_id"}], "relationship_type": "many-to-one"}, {"child_table": "purchase", "parent_table": "vendor", "key_column_mapping": [{"parent_column": "id", "child_column": "vendor_id"}], "relationship_type": "many-to-one"}, {"child_table": "purchase", "parent_table": "employee", "key_column_mapping": [{"parent_column": "id", "child_column": "employee_id"}], "relationship_type": "many-to-one"}, {"child_table": "purchase", "parent_table": "tax_code", "key_column_mapping": [{"parent_column": "id", "child_column": "tax_code_id"}], "relationship_type": "many-to-one"}]}