{"knowledge_blocks": [{"purpose": "Produce monthly utilisation metrics (available hours, used hours, utilisation %) for each service type over the most recent six-month window.", "business_rules": ["Consider only employees with Role = 'Technician'.", "Used hours per month & service type = SUM(ServiceRecords.Time_Taken) ÷ 60 (convert minutes to hours).", "Determine technician_count per service type from EmployeeData (Role = 'Technician').", "Available hours per month & service type = technician_count × weeks_in_month × 40 (standard 40-hour work-week).", "Date window = first day of month 5 months ago up to first day of next month (six complete months).", "Include all service types from ServiceTypes, even if no usage in a month (report zero used hours).", "Round utilisation percentage to two decimals; if available_hours = 0, return 0 to avoid division error."], "linked_knowledge_block_references": ["finance_monthly_salary_trend_1"], "sql_query_logic": "1. params CTE defines start_month (CURRENT_DATE – 5 months, truncated to month) and end_month (first day of next month).\n2. months CTE collects distinct month_start values from ServiceRecords within window.\n3. service_types CTE pulls all Service_Type_ID & Description.\n4. used_hours CTE aggregates Time_Taken (÷60) by month_start & Service_Type_ID for technicians.\n5. tech_counts CTE counts technicians per Service_Type_ID.\n6. availability CTE crosses months with service_types, joins tech_counts, and computes available_hours = weeks_in_month × 40 × tech_count (weeks via DATEDIFF('week', month_start, month_start + 1 month)).\n7. Final SELECT joins availability to used_hours and service_types; calculates utilisation %.", "sql": "WITH params AS (\n  SELECT DATE_TRUNC('month', DATEADD(month, -5, CURRENT_DATE)) AS start_month,\n         DATE_TRUNC('month', DATEADD(month,  1, CURRENT_DATE)) AS end_month\n),\nmonths AS (\n  SELECT DISTINCT DATE_TRUNC('month', sr.Service_Date) AS month_start\n  FROM superlube.ServiceRecords sr, params p\n  WHERE sr.Service_Date >= p.start_month AND sr.Service_Date < p.end_month\n),\nservice_types AS (\n  SELECT Service_Type_ID, Description FROM superlube.ServiceTypes\n),\nused_hours AS (\n  SELECT DATE_TRUNC('month', sr.Service_Date) AS month_start,\n         sr.Service_Type_ID,\n         SUM(sr.Time_Taken)/60.0 AS used_hours\n  FROM superlube.ServiceRecords sr\n  JOIN params p ON sr.Service_Date >= p.start_month AND sr.Service_Date < p.end_month\n  WHERE sr.Employee_ID IN (SELECT Employee_ID FROM superlube.EmployeeData WHERE Role = 'Technician')\n  GROUP BY 1, sr.Service_Type_ID\n),\ntech_counts AS (\n  SELECT ed.Service_Type_ID, COUNT(*) AS tech_count\n  FROM superlube.EmployeeData ed\n  WHERE ed.Role = 'Technician'\n  GROUP BY ed.Service_Type_ID\n),\navailability AS (\n  SELECT m.month_start,\n         st.Service_Type_ID,\n         tc.tech_count,\n         DATEDIFF('week', m.month_start, DATEADD(month, 1, m.month_start)) * 40 * tc.tech_count AS available_hours\n  FROM months m\n  CROSS JOIN service_types st\n  JOIN tech_counts tc ON tc.Service_Type_ID = st.Service_Type_ID\n)\nSELECT st.Description                       AS Service_Type,\n       TO_CHAR(a.month_start,'YYYY-MM')     AS Month,\n       a.available_hours                    AS Total_Available_Hours,\n       COALESCE(u.used_hours,0)             AS Total_Used_Hours,\n       ROUND(CASE WHEN a.available_hours=0 THEN 0 ELSE COALESCE(u.used_hours,0)/a.available_hours*100 END,2) AS Utilization_Percentage\nFROM availability a\nLEFT JOIN used_hours u ON a.month_start = u.month_start AND a.Service_Type_ID = u.Service_Type_ID\nJOIN service_types st ON st.Service_Type_ID = a.Service_Type_ID\nORDER BY a.month_start, st.Description;", "data_processing_logic": "1. Execute SQL and save result.\n2. If tool returns .dat, export unchanged to CSV (monthly_service_utilization_6mo.csv).\n3. Present table in answer and optionally highlight key insights.", "donts": "• Do not include non-technician roles when counting technicians or summing used hours.\n• Do not forget to convert Time_Taken (minutes) to hours before aggregation.\n• Avoid dividing by zero when available_hours = 0; default utilisation to 0.\n• Do not omit service types with zero usage; cross-join months with all service types to give full grid.", "additional_details": "Weeks_in_month is calculated via DATEDIFF('week', month_start, month_start + 1 month), ensuring correct handling of partial weeks at month boundaries. Adjust schema names (superlube.*) if environment differs.", "block_id": "operations_service_utilization_trend_1", "pattern_keys": ["operations_service_utilization_trend"]}, {"purpose": "Compute and present the monthly trend of total salary payments for a specified calendar year.", "business_rules": ["Use Salaries.Pay_Date as the reference date for payment timing.", "Sum Salaries.Amount; all records are considered valid because the dataset contains no void/reversal status flags.", "Report results at a monthly granularity, ordered chronologically."], "linked_knowledge_block_references": [], "sql_query_logic": "Aggregate salaries by month for the target year, using an inclusive lower-bound and exclusive upper-bound date filter to capture the full 12-month window.", "sql": "SELECT\n    DATE_TRUNC('month', Pay_Date) AS Salary_Month,\n    SUM(Amount)              AS Total_Salary\nFROM Salaries\nWHERE Pay_Date >= '{YEAR}-01-01'\n  AND Pay_Date <  '{YEAR_PLUS_1}-01-01'\nGROUP BY Salary_Month\nORDER BY Salary_Month ASC;", "data_processing_logic": "1. Load SQL result into DataFrame sorted by Salary_Month.\n2. Add column Month = format(Salary_Month, 'YYYY-MM').\n3. Calculate MoM_Diff  = Total_Salary.diff().\n4. Calculate MoM_Pct_Change = (MoM_Diff / Total_Salary.shift(1)) * 100.\n5. Output table/CSV with columns: Month, Total_Salary, MoM_Diff, MoM_Pct_Change.\n6. Optionally generate line chart of Total_Salary over Month.", "donts": "• Do NOT filter on non-existent status or reversal columns.\n• Do NOT assume data exists for every month; handle missing months gracefully.\n• Avoid hard-coding the year—parameterize '{YEAR}'.", "additional_details": "Salaries table links to EmployeeData via Employee_ID; joins are unnecessary for overall trend but may be used for employee-level filtering when required. If only partial year data exists, clarify this limitation in the narrative.", "block_id": "finance_monthly_salary_trend_1", "pattern_keys": ["finance_monthly_salary_trend", "finance_monthly_rev_labour_agg"]}, {"purpose": "Generate a monthly gross-profit percentage relative to labour costs (and accompanying bar chart) for the most recent six complete months.", "business_rules": ["Gross-profit percentage = (Revenue – LabourCost) / LabourCost * 100, rounded to two decimals.", "Revenue is the monthly sum of Invoices.Total_Charge using Invoice_Date.", "LabourCost is the monthly sum of Salaries.Amount using Pay_Date.", "Define month_start as the first day of each month via DATE_TRUNC('month', <date>).", "Only include months where LabourCost is not null and greater than zero (complete months).", "Select the latest six months that satisfy the above completeness rule; if fewer than six exist in the last 6-month window, widen the look-back (e.g., 12 months) until six complete months are found.", "Order result chronologically (ascending month_start)."], "linked_knowledge_block_references": [], "sql_query_logic": "1. Aggregate revenue per month from superlube.Invoices.\n2. Aggregate labour_cost per month from superlube.Salaries.\n3. FULL JOIN or UNION both aggregates on month_start.\n4. Limit raw pull to the last 12 (or more) calendar months to ensure sufficient complete data.\n5. After retrieval, let downstream processing filter for completeness and choose the latest six valid months.", "sql": "WITH rev AS (\n  SELECT DATE_TRUNC('month', Invoice_Date) AS month_start,\n         SUM(Total_Charge)            AS revenue\n  FROM superlube.Invoices\n  WHERE Invoice_Date >= DATE_TRUNC('month', CURRENT_DATE - INTERVAL '11 months')\n  GROUP BY 1\n),\nlab AS (\n  SELECT DATE_TRUNC('month', Pay_Date) AS month_start,\n         SUM(Amount)                   AS labour_cost\n  FROM superlube.Salaries\n  WHERE Pay_Date >= DATE_TRUNC('month', CURRENT_DATE - INTERVAL '11 months')\n  GROUP BY 1\n)\nSELECT COALESCE(rev.month_start, lab.month_start) AS month_start,\n       revenue,\n       labour_cost\nFROM rev\nFULL JOIN lab USING (month_start)\nORDER BY month_start;", "data_processing_logic": "load data; filter = rows where labour_cost != null and labour_cost > 0; sort by month_start ascending; take last 6 rows; compute gross_profit_pct = round(((revenue - labour_cost) / labour_cost) * 100, 2); output CSV [month_start YYYY-MM, revenue, labour_cost, gross_profit_pct]; build bar chart gross_profit_pct vs month_start.", "donts": "Do NOT include months with null or zero labour_cost (will give division errors and mislead percentage). Do NOT assume the last six calendar months are complete; always verify labour_cost data. Do NOT compute gross-profit percentage using revenue as denominator; use labour_cost.", "additional_details": "Output artefacts: CSV file (gross_profit_pct_by_month.csv) and bar chart (gross_profit_pct_bar.png). This block assumes superlube schema names; adjust if schema aliases change.", "block_id": "finance_monthly_rev_labour_agg_1", "pattern_keys": ["finance_monthly_rev_labour_agg", "finance_monthly_gprofit_pct_trend"]}, {"purpose": "Generate total annual revenue for a user-specified multi-year window (e.g., 2020-2025) from operational invoice data while explicitly excluding any QuickBooks or accounting-ledger sources, and output both a CSV and bar chart.", "business_rules": ["Use table superlube.Invoices as the authoritative operational revenue source.", "Revenue amount column = Invoices.Total_Charge.", "Transaction date column = Invoices.Invoice_Date.", "Include only invoices with Invoice_Date >= <START_YEAR>-01-01 and < END_YEAR + 1 >-01-01.", "Aggregate revenue totals by calendar year (EXTRACT(year FROM Invoice_Date)).", "Invoices table contains no status flags; therefore, all rows are considered valid unless a status column is added in future.", "Exclude any data residing in QuickBooks_Raw (or other accounting-ledger schemas)."], "linked_knowledge_block_references": ["finance_monthly_rev_labour_agg_1"], "sql_query_logic": "From superlube.Invoices, filter Invoice_Date between the start and end boundaries (inclusive start, exclusive end of next year). Group by EXTRACT(year FROM Invoice_Date) to produce one row per year, summing Total_Charge as Total_Revenue; order results ascending by year.", "sql": "SELECT\n    EXTRACT(year FROM Invoice_Date) AS Revenue_Year,\n    SUM(Total_Charge)              AS Total_Revenue\nFROM superlube.Invoices\nWHERE Invoice_Date >= '2020-01-01'       -- parameterise start date\n  AND Invoice_Date <  '2026-01-01'       -- parameterise end boundary (END_YEAR+1)\nGROUP BY Revenue_Year\nORDER BY Revenue_Year ASC;", "data_processing_logic": "1. Load SQL result into a DataFrame.\n2. Save as CSV (e.g., revenue_by_year.csv).\n3. Generate bar chart with x=Revenue_Year, y=Total_Revenue; label axes and title.\n4. Save chart as PNG (e.g., revenue_by_year_bar.png).\n5. Provide both files to user; optionally embed table preview.", "donts": "• Do NOT pull data from QuickBooks_Raw or other accounting-ledger schemas.\n• Do NOT mix monthly and yearly aggregation; always aggregate by calendar year for this report.\n• Do NOT include dates outside the specified range.\n• Do NOT attempt to filter by non-existent status flags unless they are later introduced.", "additional_details": "If future schema changes introduce invoice status or cancellation flags, update the business rules to include only completed/valid statuses. The same pattern can be reused for any multi-year window by parameterising start and end years.", "block_id": "finance_annual_rev_trend_1", "pattern_keys": ["finance_annual_rev_trend"]}, {"purpose": "Complete workflow for creating expenses in QuickBooks based on purchase receipts uploaded by the user.", "workflow_steps": {"1. Information Extraction + Accounting Master Data Loading": ["Call Document_Data_Retriever for all receipts and Accounting_Master_Data_Loader in parallel for better efficiency.", "Use the `Document_Data_Retriever` tool to extract the following information as two CSV files: one for header details and one for line items. Ensure all required fields below are captured with exact names.", {"CSV files": [{"file_name": "{receipt_name}_header.csv", "columns": ["a) **payment_method_details**: Identify whether the expense was paid by CreditCard, Cash, or Check. If CreditCard, extract all available metadata such as bank or institution name, masked card number, etc.", "b) **tx_date**: Date of transaction in YYYY-MM-DD format.", "c) **tx_time**: Time of the transaction in HH:MM:SS (24 hour) format, if available in the receipt, else keep this empty", "d) **sub_total**: Subtotal amount (before tax).", "e) **total_amount**: Final amount charged (including taxes).", "f) **payee_name**: Full vendor name – the merchant who sold the goods/services, not the billed-to party. Note that home company is ZOOMi Technologies Inc, 84 Leon Bell Dr, Winnipeg, and it should never be intepreted as a vendor.", "g) **payee_address**: Vendor address or location information if available.", "h) **tax_details**: Tax breakdown as shown on the receipt.", "i) **currency**: Currency code if specified on the receipt. If absent, infer from vendor location (e.g., Google -> USD). Leave blank if insufficient information.", "e) **invoice_number**: Invoice number in the receipt if available, otherwise empty"]}, {"file_name": "{receipt_name}_lines.csv", "columns": ["a) **description**: Extract the most complete description available. Do not truncate model names, item codes, or product details.", "b) **amount**: Line item subtotal (before tax).", "c) **tax_details**: Line item tax information if available."]}]}, "Note: Ensure extraction captures both line items and tax details so that the sum of line item amounts plus taxes equals the total receipt amount. This serves as a consistency check for completeness and accuracy.", "", "Use the `Accounting_Master_Data_Loader` tool to retrieve the following master data for mapping:", "- `expense_accounts` (for line item matching)", "- `bank_accounts` (used if payment method is Check)", "- `credit_card_accounts` (used if payment method is CreditCard)", "- `tax_codes` (to map GST, PST, etc.)", "- `payment_methods` (e.g., Mastercard, Visa, Cash)"], "2. Accounting Decision Making": ["For each purchase, determine the following fields as an expert accountant:", "a) Vendor name: the merchant who provided the goods/services (not the billed-to company).", "b) Payment type: Must be one of CreditCard, Cash, or Check based on extracted payment_method.", "c) Payment account: Match to the correct credit card or bank account from master data based on the extracted payment_method_details. If you don't have enough information, set to 'Unrecognized Payment Account'.", "d) Payment method: Match to the correct payment method from master data (e.g., Visa, Cash).", "e) Global tax calculation: Apply the following rules - 'TaxInclusive' if receipt explicitly states taxes included (e.g., 'Total includes GST'); 'TaxExcluded' if taxes are shown as separate lines; 'NotApplicable' if no tax information is shown and totals match line item sums.", "f) Currency: If not extracted or invalid, infer from vendor location.", "", "For each line item:", "a) Expense account: Use both vendor and line item description to determine the expense category. Then you must assign the most accurate account from expense_accounts for that category.", "b) Tax code: Determine based on line item nature, receipt tax details, and vendor location. Choose the best match from tax_codes.", "", "Do NOT instruct Data_Processor to perform accounting decision making. All mapping (vendor, account, tax code assignment, etc.) must be decided in Step 2 before calling Data_Processor."], "3. Data Preparation": ["Use the `Data_Processor` tool to generate two CSVs: one with header-level purchase information and another with line item details.", "Instruct the 'Data_Processor' with exact values from extracted info and accounting decisions.", {"fileName": "purchases.csv", "description": "One row per purchase/receipt.", "columns": {"purchase_key": "Unique reference ID for the purchase (incremental or derived from file name).", "payee_name": "Vendor name determined above.", "payee_address": "Vendor address if available.", "payment_type": "Payment type determined above.", "transaction_date": "Transaction date in YYYY-MM-DD format from receipt.", "timestamp": "Transaction time in HH:MM:SS (24 hour) format if available in extracted info.", "invoice_number": "Invoice number if available in extracted info.", "total_amount": "As extracted.", "attachment_file_name": "Uploaded receipt file name.", "payment_account_id": "Payment account ID determined above.", "payment_method_id": "Payment method ID determined above.", "global_tax_calculation": "As determined above.", "currency": "Currency code as extracted or inferred."}}, {"fileName": "purchase_lines.csv", "description": "One row per line item from receipts. Preserve order and allocation.", "columns": {"purchase_key": "Reference to parent purchase in purchases.csv.", "line_number": "Incremental line item number per purchase.", "detail_type": "Always 'AccountBasedExpenseLineDetail'.", "line_amount": "Pre-tax line subtotal.", "description": "Detailed item description from extraction.", "expense_account_id": "Expense account ID determined above.", "tax_code_id": "Tax code ID determined above."}}], "4. QuickBooks Updating": ["Call the `Accounting_API_Tool` with api_task='add_expenses' and data_source_name='receipt' to update QuickBooks using the generated CSVs:", "- purchases.csv", "- purchase_lines.csv"], "5. Verification and Corrections": ["Recall the information extracted from the receipt and cross check it with the confirmation of QuickBooks updated received from step 4 to identify if any issues with the information updated in QuickBooks.", {"verification checklist in QuickBooks expense records:": ["a) Payment accounts are correct.", "b) Transaction dates match receipts.", "c) Total amounts align, accounting for cases where QuickBooks converts to home currency.", "d) Vendors are correctly assigned.", "e) Expense accounts/categories are correctly mapped in all line items.", "f) Sum of line items matches total receipt amount.", "g) Tax codes are correctly applied per line item.", "h) Global tax calculation is set correctly.", "i) Private note field must contain 'source=receipt' for all created expenses."]}, "If header data is wrong, prepare 'purchase_updates.csv' (purchase_id + same columns as purchases.csv) with corrected values.", "If line items are wrong, prepare 'purchase_line_updates.csv' (purchase_id + same columns as purchase_lines.csv) with corrected values.", "Call `Accounting_API_Tool` with api_task='update_expenses' to apply corrections."]}, "donts": ["Do NOT use SQL tool to find account IDs, tax code IDs, or payment method IDs. Always use Accounting_Master_Data_Loader."], "additional_notes": ["Decisions on accounts, taxes, and payment methods must follow professional accounting judgment.", "Always give unique table names when calling Document_Data_Retriever.", "Run Document_Data_Retriever only once per document. Extract both header and line details in a single pass.", "The CSV file that submits to the 'Accounting_API_Tool' must always be purchase.csv and purchase_lines.csv.", "Call Document_Data_Retriever and Accounting_Master_Data_Loader together at the first cycle (parallel execution)."], "block_id": "quickbooks_expense_creation", "linked_knowledge_block_references": [], "pattern_keys": ["quickbooks_expense_creation", "quickbooks_purchase_creation"]}, {"purpose": "Complete workflow for reconciling bank transactions by matching bank statements with QuickBooks general ledger.", "workflow_steps": [{"step": "1: Extract transactions from the uploaded statement", "procedure": "Use the `Document_Data_Retriever` tool to extract information into two structured CSV files. When instructing the tool, you MUST explicitly list all required fields with their guidelines exactly as described below. Do not omit or assume fields — the extractor must be told precisely what to capture. If a field is unavailable in the document, leave it blank instead of improvising.", "extraction_outputs": [{"file_name": "{statement_file_name}_summary.csv", "description": "A single-row CSV containing header information of the bank statement (typically found on the first page).", "columns": ["institution_name – Bank or financial institution name", "account_info – Account details (e.g., bank account, credit card number, or account type)", "start_date – Statement start date in YYYY-MM-DD format", "end_date – Statement end date in YYYY-MM-DD format", "starting_balance – Opening balance of the period (if available)", "ending_balance – Closing balance of the period (if available)"]}, {"file_name": "{statement_file_name}_transactions.csv", "description": "A multi-row CSV with one record for each transaction in the statement (may span multiple pages).", "columns": ["tx_date – Transaction date (YYYY-MM-DD format)", "description – Transaction description/details as shown in the statement", "amount – Transaction amount (always stored as a positive number, even for debits/credits)", "current_balance – Account balance after the transaction (if available)"]}]}, {"step": "2: Load required master data", "procedure": "Use the `Accounting_Master_Data_Loader` tool to retrieve master data, including: `bank_accounts`, `credit_card_accounts`, `expense_accounts`, and `payment_methods`. Identify the correct QuickBooks account IDs for both the statement account and the 'Unrecognized Payment' account."}, {"step": "3: Match statement transactions with QuickBooks records", "procedure": "Call the `Accounting_API_Tool` with `api_task = 'match_statement'`, `data_type_list = ['Purchase']`, and `data_file_names = ['{statement_file_name}_transactions.csv']`. The tool will attempt to match each statement transaction with existing QuickBooks transactions recorded between 5 days before the statement start date and the statement end date. The tool will return a CSV named `statement_qb_matches.csv`, which includes the original statement transactions plus the following new columns: `match_status` (Matched / Unmatched), `qb_transaction_id`, `qb_txn_date`, `qb_payee`, `qb_amount`, and `qb_line_descriptions`."}, {"step": "4: Update QuickBooks with statement transactions", "procedure": "Prepare datasets for adding missed purchase transactions to QuickBooks and updating existing purchases with correct transaction amounts and payment accounts.", "sub_steps": [{"step": "a) Accounting decision making", "procedure": {"description": "For each purchase transaction, determine the following fields as an expert accountant. Do NOT instruct `Data_Processor` to make accounting decisions. All mappings (vendor, account, tax code, etc.) must be decided here before passing data forward.", "fields_list": ["Vendor name – The merchant who provided the goods/services. Derive from the statement description.", "Payment type – Must be one of CreditCard, Cash, or Check, based on the type of statement.", "Payment account – Must be the exact account from the statement.", "Payment method – Match to the correct payment_method from master data.", "Expense account – Assign the most accurate account from `expense_accounts` based on the transaction description."]}}, {"step": "b) Prepare dataset for adding missed transactions", "procedure": {"description": "Instruct the `Data_Processor` with exact values (from extraction and accounting decisions) to create two CSVs: one for purchase headers and one for purchase lines.", "files": [{"fileName": "purchases.csv", "description": "One row per purchase/receipt.", "columns": {"purchase_key": "Unique reference ID for the purchase (incremental).", "payee_name": "Vendor name determined above.", "payment_type": "Payment type determined above.", "total_amount": "Amount from the statement record.", "transaction_date": "Transaction date in YYYY-MM-DD format.", "payment_account_id": "ID of the payment account determined above.", "payment_method_id": "ID of the payment method determined above."}}, {"fileName": "purchase_lines.csv", "description": "One row per line item. If no breakdown is available, create a single line item with the full transaction amount.", "columns": {"purchase_key": "Reference to parent purchase in purchases.csv.", "line_number": "Line item number (incremental per purchase).", "detail_type": "Always 'AccountBasedExpenseLineDetail'.", "line_amount": "Line item subtotal, or full amount if no breakdown.", "description": "Line item description if available, otherwise the statement description.", "expense_account_id": "ID of the expense account determined above."}}]}}, {"step": "c) Prepare dataset for updating existing transactions", "procedure": {"description": "Instruct the `Data_Processor` with exact values (from extraction and accounting decisions) to create `purchase_updates.csv` for updating existing QuickBooks records.", "files": [{"fileName": "purchase_updates.csv", "description": "One row per purchase to update.", "columns": {"purchase_id": "Exact ID of the existing QuickBooks purchase record.", "payment_type": "Payment type determined above.", "total_amount": "Amount from the statement record.", "transaction_date": "Transaction date in YYYY-MM-DD format.", "payment_account_id": "ID of the payment account determined above.", "payment_method_id": "ID of the payment method determined above."}}]}}, {"step": "d) Add missed transactions to QuickBooks", "procedure": "Call the `Accounting_API_Tool` with `api_task = 'add_expenses'` and pass `purchases.csv` and `purchase_lines.csv`. Set `data_source_name = 'statement'`."}, {"step": "e) Update existing transactions in QuickBooks", "procedure": "Call the `Accounting_API_Tool` with `api_task = 'update_expenses'` and pass `purchase_updates.csv`. Set `data_source_name = 'statement'`."}]}, {"step": "5: Verification and corrections", "procedure": ["Review the results of step 4 to identify any corrections needed.", "If expense header data requires correction, prepare `purchase_updates.csv` again with corrected values.", "If expense line items require correction, prepare `purchase_line_updates.csv` with corrected values.", "Use the `Accounting_API_Tool` with `api_task = 'update_expenses'` and/or `update_expense_lines` as required."]}, {"step": "6: Provide final report", "procedure": "Present the results from steps 4 and 5 as a complete report of statement matching and QuickBooks updates."}], "linked_knowledge_block_references": [], "donts": ["Use 'SQL_Data_Retrieval_Tool' to query transactions in the required date range (Instead use 'Accounting_API_Tool' as instructed above).", "Instruct 'Data_Processing_Tool' to use any fuzzy or other library for transaction matching (It has built in capability for cognitive matching)."], "additional_notes": ["1. <PERSON><PERSON><PERSON> execute step 1 and step 2a to make it more efficient.", "2. Make sure you set the exact CSV file names as instructed when calling Accounting API tool."], "block_id": "quickbooks_bank_reconcile", "pattern_keys": ["quickbooks_bank_rec", "quickbooks_bank_statement_matching"]}, {"purpose": "Complete workflow for matching bank statements with QuickBooks.", "workflow_steps": [{"step": "1: Extract transactions from the uploaded statement", "procedure": "Use the `Document_Data_Retriever` tool to extract information into two structured CSV files. When instructing the tool, you MUST explicitly list all required fields with their guidelines exactly as described below. Do not omit or assume fields — the extractor must be told precisely what to capture.", "extraction_outputs": [{"file_name": "{statement_file_name}_summary.csv", "description": "A single-row CSV containing the header information of the bank statement (typically on the first page).", "columns": ["institution_name – Bank or financial institution name", "account_info – Account details (e.g., bank account, credit card number, or account type)", "start_date – Statement start date in YYYY-MM-DD format", "end_date – Statement end date in YYYY-MM-DD format", "starting_balance – Opening balance of the period (if available)", "ending_balance – Closing balance of the period (if available)"]}, {"file_name": "{statement_file_name}_transactions.csv", "description": "A multi-row CSV with one record for each transaction in the statement (may span multiple pages).", "columns": ["tx_date – Transaction date (YYYY-MM-DD format)", "description – Transaction description/details as shown in the statement", "amount – Transaction amount (always stored as a positive number, even for debits/credits)", "current_balance – Account balance after the transaction (if available)"]}]}, {"step": "2: Load required master data", "procedure": "Use the `Accounting_Master_Data_Loader` tool to retrieve master data, including: `bank_accounts`, `credit_card_accounts`, `expense_accounts`, and `payment_methods`. Identify the correct QuickBooks account IDs for both the statement account and the 'Unrecognized Payment' account."}, {"step": "3: Match statement transactions with QuickBooks records", "procedure": "Call the `Accounting_API_Tool` with `api_task = 'match_statement'`, `data_type_list = ['Purchase']`, and `data_file_names = ['{statement_file_name}_transactions.csv']`. The tool will attempt to match each statement transaction with existing QuickBooks transactions recorded between 5 days before the statement start date and the statement end date. The tool will return a CSV named `statement_qb_matches.csv`, which includes the original statement transactions plus the following new columns: `match_status` (Matched / Unmatched), `qb_transaction_id`, `qb_txn_date`, `qb_payee`, `qb_amount`, and `qb_line_descriptions`."}, {"step": "4: Generate and present the final matching report", "procedure": "Provide the results from Step 3 to the user as a complete transaction matching report."}], "linked_knowledge_block_references": [], "donts": [], "additional_notes": ["1. <PERSON><PERSON><PERSON> execute step 1 and step 2a to make it more efficient."], "block_id": "quickbooks_bank_match", "pattern_keys": ["quickbooks_bank_statement_matching"]}], "knowledge_tree": {"Expenses": [{"pattern": "Expenses creation and attachment workflow", "pattern_key": "quickbooks_expense_creation"}], "Finance": [{"pattern": "Monthly revenue and labour cost aggregation", "pattern_key": "finance_monthly_rev_labour_agg"}, {"pattern": "Monthly gross-profit percentage trend analysis", "pattern_key": "finance_monthly_gprofit_pct_trend"}, {"pattern": "Monthly salary payment trend analysis", "pattern_key": "finance_monthly_salary_trend"}, {"pattern": "Annual revenue trend analysis", "pattern_key": "finance_annual_rev_trend"}], "Bank Statement Matching": [{"pattern": "Bank statement matching with QuickBooks", "patten_key": "quickbooks_bank_statement_matching"}, {"pattern": "Bank statement reconcile workflow", "pattern_key": "quickbooks_bank_rec"}]}}