#!/usr/bin/env python3
"""
Demonstration script for the enhanced generate_reference_key method
with robust timestamp validation.
"""

import sys
import os
import re
import xxhash

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(__file__))


def validate_timestamp_format(timestamp: str) -> str:
    """
    Validate timestamp format and return normalized timestamp.
    Supports only 24-hour format:
    - HH:MM (24-hour format)
    - HH:MM:SS (24-hour format with seconds)
    - H:MM or H:MM:SS (single digit hour)

    This is a copy of the simplified method from AccountingApiAgent for demonstration.
    """
    if not timestamp:
        return ""

    # Remove extra whitespace
    timestamp = timestamp.strip()

    # Pattern for 24-hour format: HH:MM or HH:MM:SS (with optional single digit hour)
    pattern = r"^([0-1]?[0-9]|2[0-3]):([0-5][0-9])(?::([0-5][0-9]))?$"

    match = re.match(pattern, timestamp)
    if match:
        hour = int(match.group(1))
        minute = int(match.group(2))
        second = int(match.group(3)) if match.group(3) else 0

        # Validate time components
        if 0 <= hour <= 23 and 0 <= minute <= 59 and 0 <= second <= 59:
            return f"{hour:02d}:{minute:02d}:{second:02d}"

    # Invalid timestamp format
    return ""


def generate_reference_key(vendor_name: str, transaction_date: str, timestamp: str, invoice_number: str):
    """
    Generate reference_key from vendor name, transaction date, timestamp and invoice number.
    This is a copy of the enhanced method from AccountingApiAgent for demonstration.
    """
    key_input = vendor_name + transaction_date

    # Validate and process timestamp if provided
    if timestamp and timestamp.strip():
        validated_timestamp = validate_timestamp_format(timestamp.strip())
        if validated_timestamp:
            # Give priority to timestamp - append to key_input for uniqueness
            key_input += validated_timestamp
            print(f"  ✓ Timestamp '{timestamp}' validated and normalized to '{validated_timestamp}'")
        else:
            print(f"  ⚠ Timestamp '{timestamp}' is invalid and will be ignored")

    # Add invoice number if provided for additional uniqueness
    if invoice_number and invoice_number.strip():
        key_input += invoice_number.strip()
        print(f"  ✓ Invoice number '{invoice_number}' added to key")

    reference_key = xxhash.xxh64(key_input).hexdigest()
    print(f"  → Key input: '{key_input}'")
    print(f"  → Generated reference key: {reference_key}")

    return reference_key


def demonstrate_reference_key_generation():
    """Demonstrate the enhanced reference key generation with various scenarios."""
    print("Enhanced Reference Key Generation Demonstration")
    print("=" * 60)

    # Test scenarios
    scenarios = [
        {
            "name": "Valid 24-hour timestamp",
            "vendor": "ABC Corp",
            "date": "2024-01-15",
            "timestamp": "14:30",
            "invoice": "INV001",
        },
        {
            "name": "Valid 24-hour timestamp with seconds",
            "vendor": "XYZ Ltd",
            "date": "2024-01-15",
            "timestamp": "09:30:45",
            "invoice": "INV002",
        },
        {
            "name": "Valid single digit hour",
            "vendor": "DEF Inc",
            "date": "2024-01-15",
            "timestamp": "2:45",
            "invoice": "INV003",
        },
        {
            "name": "Invalid timestamp (ignored)",
            "vendor": "GHI Corp",
            "date": "2024-01-15",
            "timestamp": "25:30",
            "invoice": "INV004",
        },
        {"name": "Empty timestamp", "vendor": "JKL Ltd", "date": "2024-01-15", "timestamp": "", "invoice": "INV005"},
        {
            "name": "Whitespace in timestamp",
            "vendor": "MNO Inc",
            "date": "2024-01-15",
            "timestamp": "  23:45  ",
            "invoice": "INV006",
        },
        {"name": "No invoice number", "vendor": "PQR Corp", "date": "2024-01-15", "timestamp": "16:20", "invoice": ""},
        {
            "name": "Midnight in 24-hour format",
            "vendor": "STU Ltd",
            "date": "2024-01-15",
            "timestamp": "00:00",
            "invoice": "INV007",
        },
        {
            "name": "Noon in 24-hour format",
            "vendor": "VWX Inc",
            "date": "2024-01-15",
            "timestamp": "12:00",
            "invoice": "INV008",
        },
    ]

    for i, scenario in enumerate(scenarios, 1):
        print(f"\nScenario {i}: {scenario['name']}")
        print("-" * 40)
        print(f"Vendor: {scenario['vendor']}")
        print(f"Date: {scenario['date']}")
        print(f"Timestamp: '{scenario['timestamp']}'")
        print(f"Invoice: '{scenario['invoice']}'")
        print("Processing:")

        key = generate_reference_key(scenario["vendor"], scenario["date"], scenario["timestamp"], scenario["invoice"])
        print()

    # Demonstrate uniqueness
    print("\n" + "=" * 60)
    print("Demonstrating Key Uniqueness")
    print("=" * 60)

    base_vendor = "Test Vendor"
    base_date = "2024-01-15"
    base_invoice = "INV001"

    # Same data should produce same key
    key1 = generate_reference_key(base_vendor, base_date, "9:30", base_invoice)
    key2 = generate_reference_key(base_vendor, base_date, "09:30", base_invoice)  # Same time, different format

    print(f"\nKey uniqueness test:")
    print(f"9:30 vs 09:30 (same time, different format): {'SAME' if key1 == key2 else 'DIFFERENT'}")

    # Different timestamps should produce different keys
    key3 = generate_reference_key(base_vendor, base_date, "10:30", base_invoice)
    print(f"09:30 vs 10:30 (different time): {'SAME' if key1 == key3 else 'DIFFERENT'}")

    print("\n🎉 Demonstration complete!")


if __name__ == "__main__":
    demonstrate_reference_key_generation()
