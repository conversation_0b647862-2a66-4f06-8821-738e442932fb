Python code:

```python
import pandas as pd
from metalake import load_data
import re

chat_id = "68ceae7d5ab71dd5d7ccddd8"

# 1. Load statement_summary_raw.dat and extract balances
df_summary = load_data(chat_id, "statement_summary_raw.dat")
if df_summary.empty:
    print("statement_summary_raw.dat is empty. Cannot extract balances.")
else:
    # Remove $ and parentheses, convert to float
    def parse_amount(s):
        if pd.isnull(s):
            return None
        s = s.replace("$", "").replace(",", "").strip()
        negative = False
        if s.startswith('(') and s.endswith(')'):
            negative = True
            s = s[1:-1]
        try:
            val = float(s)
        except Exception:
            return None
        return -val if negative else val
    
    starting_balance_row = df_summary[df_summary['label'] == 'Starting Cash Balance']
    ending_balance_row = df_summary[df_summary['label'] == 'Ending Cash Balance']
    
    starting_balance = parse_amount(starting_balance_row['amount'].values[0]) if not starting_balance_row.empty else None
    ending_balance = parse_amount(ending_balance_row['amount'].values[0]) if not ending_balance_row.empty else None

# 2. Load statement_details_raw.dat and extract date range
df_details = load_data(chat_id, "statement_details_raw.dat")
if df_details.empty or 'Value' not in df_details.columns:
    print("statement_details_raw.dat is empty or missing Value column. Cannot extract period.")
    start_date = end_date = None
else:
    period_value = df_details['Value'].iloc[0]
    if ' to ' in period_value:
        start_date, end_date = period_value.split(' to ')
        start_date = start_date.strip()
        end_date = end_date.strip()
    else:
        start_date = end_date = None

# 3. Load recipient_address_raw.dat and extract account_info
df_recipient = load_data(chat_id, "recipient_address_raw.dat")
if df_recipient.empty or 'account_name' not in df_recipient.columns:
    print("recipient_address_raw.dat is empty or missing account_name column.")
    account_info = None
else:
    account_info = df_recipient['account_name'].iloc[0]

# 4. Set institution_name
institution_name = "Float Financial Solutions Inc."

# 5. Build summary dataframe
summary_data = {
    'institution_name': [institution_name],
    'account_info': [account_info],
    'start_date': [start_date],
    'end_date': [end_date],
    'starting_balance': [f"${starting_balance:,.2f}" if starting_balance is not None else None],
    'ending_balance': [f"${ending_balance:,.2f}" if ending_balance is not None else None]
}
summary_df = pd.DataFrame(summary_data)

# Save summary_df to CSV
summary_csv = "files/float_statement_2025-08-01_2025-08-31_summary.csv"
summary_df.to_csv(summary_csv, index=False)

# 7. Load account_activity_raw.dat and process transactions
df_activity = load_data(chat_id, "account_activity_raw.dat")
if df_activity.empty:
    print("account_activity_raw.dat is empty. No transactions available.")
    transactions_df = pd.DataFrame(columns=["tx_date", "description", "amount", "current_balance"])
else:
    # Rename columns
    rename_map = {'Date': 'tx_date', 'Details': 'description', 'Amount': 'amount'}
    transactions_df = df_activity.rename(columns=rename_map)
    # Format amount as currency string with two decimals
    def format_amt(amt):
        # Remove $/parentheses etc, then format
        if pd.isnull(amt):
            return ""
        s = str(amt).replace("$", "").replace(",", "").strip()
        negative = False
        if s.startswith('(') and s.endswith(')'):
            negative = True
            s = s[1:-1]
        try:
            val = float(s)
        except Exception:
            return amt
        val = -val if negative else val
        return f"${val:,.2f}"
    transactions_df['amount'] = transactions_df['amount'].apply(format_amt)
    # Add current_balance column as empty string
    transactions_df['current_balance'] = ""
    # Select and order columns
    transactions_df = transactions_df[["tx_date", "description", "amount", "current_balance"]]

# Save transactions_df to CSV
transactions_csv = "files/float_statement_2025-08-01_2025-08-31_transactions.csv"
transactions_df.to_csv(transactions_csv, index=False)

# 9. Console output: Print summary, transaction count and first 3 rows
print("\n--- Float Statement Summary ---")
print(summary_df.to_markdown(index=False))

print(f"\nTotal transactions: {len(transactions_df)}")
if len(transactions_df) > 0:
    show_rows = min(3, len(transactions_df))
    preview = transactions_df.head(show_rows)
    print(f"First {show_rows} transaction(s):")
    print(preview.to_markdown(index=False))
else:
    print("No transactions to display.")

````

Output:

--- Float Statement Summary ---
| institution_name               | account_info           | start_date   | end_date   | starting_balance   | ending_balance   |
|:-------------------------------|:-----------------------|:-------------|:-----------|:-------------------|:-----------------|
| Float Financial Solutions Inc. | ZOOMI TECHNOLOGIES INC | 2025-08-01   | 2025-08-31 | $516.41            | $1,124.70        |

Total transactions: 10
First 3 transaction(s):
| tx_date    | description                           | amount   | current_balance   |
|:-----------|:--------------------------------------|:---------|:------------------|
| 2025-08-01 | Automatically earned monthly interest | $1.54    |                   |
| 2025-08-01 | Figma | **** 7116                     | $31.83   |                   |
| 2025-08-02 | Google Workspace | **** 3090          | $231.00  |                   |

