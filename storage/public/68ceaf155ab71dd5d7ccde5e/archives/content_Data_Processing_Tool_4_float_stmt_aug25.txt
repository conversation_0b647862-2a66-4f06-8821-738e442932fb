Python code:

```python
import pandas as pd
from metalake import load_data

chat_id = "68ceaed55ab71dd5d7ccde15"

data_file = "float_statement_2025-08-01_2025-08-31_summary.dat"
df = load_data(chat_id, data_file)

if df.empty:
    print("No data found in the specified file.")
else:
    # Select columns in required order
    columns = [
        "institution_name",
        "account_info",
        "start_date",
        "end_date",
        "starting_balance",
        "ending_balance"
    ]
    display_df = df[columns]
    print(display_df.to_markdown(index=False))

````

Output:

| institution_name               | account_info           | start_date   | end_date   | starting_balance   | ending_balance   |
|:-------------------------------|:-----------------------|:-------------|:-----------|:-------------------|:-----------------|
| Float Financial Solutions Inc. | ZOOMI TECHNOLOGIES INC | 2025-08-01   | 2025-08-31 | $516.41            | $1,124.70        |

