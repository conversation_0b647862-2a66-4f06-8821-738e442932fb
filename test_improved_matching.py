#!/usr/bin/env python3
"""
Test script to verify the improved transaction matching algorithm.
"""

import pandas as pd
import sys
import os
from datetime import datetime

# Add the current directory to the path so we can import the accounting agent
sys.path.append(os.path.dirname(__file__))

from services.tools.data_processor.accounting_api_agent import AccountingAPIAgent


class MockChatLog:
    """Mock chat log for testing"""

    def info(self, msg):
        print(f"INFO: {msg}")

    def debug(self, msg):
        print(f"DEBUG: {msg}")

    def error(self, msg):
        print(f"ERROR: {msg}")


def test_improved_matching():
    """Test the improved matching algorithm with the provided example data"""

    # Create statement transactions DataFrame
    statement_data = [
        {
            "tx_date": "2025-08-01",
            "description": "Interest Automatically earned monthly interest",
            "amount": 1.54,
            "index": 0,
        },
        {
            "tx_date": "2025-08-01",
            "description": "Purchase Figma | **** 7116 <PERSON><PERSON>",
            "amount": 31.83,
            "index": 1,
        },
        {
            "tx_date": "2025-08-02",
            "description": "Purchase Google Workspace | **** 3090 Buddhika Madduma",
            "amount": 231.00,
            "index": 2,
        },
        {
            "tx_date": "2025-08-02",
            "description": "Purchase Amazon Web Services | **** 8550 Isuru Koralage",
            "amount": 1.44,
            "index": 3,
        },
        {
            "tx_date": "2025-08-04",
            "description": "Purchase ChatGPT | **** 2477 Isuru Koralage",
            "amount": 29.88,
            "index": 4,
        },
        {
            "tx_date": "2025-08-06",
            "description": "Purchase Twilio | **** 3500 Isuru Koralage",
            "amount": 28.22,
            "index": 5,
        },
        {
            "tx_date": "2025-08-07",
            "description": "Funding Minimum CAD Cash balance threshold hit",
            "amount": 1000.00,
            "index": 6,
        },
        {
            "tx_date": "2025-08-13",
            "description": "Purchase Cursor | **** 4861 Isuru Koralage",
            "amount": 28.32,
            "index": 7,
        },
        {
            "tx_date": "2025-08-16",
            "description": "Purchase Cursor | **** 3430 Isuru Koralage",
            "amount": 28.34,
            "index": 8,
        },
        {
            "tx_date": "2025-08-27",
            "description": "Purchase Github | **** 5590 Isuru Koralage",
            "amount": 14.22,
            "index": 9,
        },
    ]

    # Create QB transactions DataFrame
    qb_data = [
        {
            "Id": 158,
            "Payee": "Google LLC",
            "TxnDate": "2025-07-31",
            "TotalAmt": 231.00,
            "LineDescription": "Google Workspace Business Starter",
        },
        {
            "Id": 159,
            "Payee": "Cursor",
            "TxnDate": "2025-08-16",
            "TotalAmt": 28.34,
            "LineDescription": "Purchase Cursor | **** 3430 | Isuru Koralage",
        },
        {
            "Id": 156,
            "Payee": "amazon web services",
            "TxnDate": "2025-08-02",
            "TotalAmt": 1.44,
            "LineDescription": "Purchase Amazon Web Services | **** 8550 | Isuru Koralage",
        },
        {
            "Id": 146,
            "Payee": "OpenAI, LLC",
            "TxnDate": "2025-08-04",
            "TotalAmt": 29.88,
            "LineDescription": "ChatGPT Plus Subscription Aug 4 – Sep 4, 2025",
        },
        {
            "Id": 122,
            "Payee": "costco wholesale #549",
            "TxnDate": "2025-08-17",
            "TotalAmt": 161.59,
            "LineDescription": "Various grocery items",
        },
        {
            "Id": 121,
            "Payee": "anthem blue cross",
            "TxnDate": "2025-08-15",
            "TotalAmt": 343.95,
            "LineDescription": "Current Charges Subtotal",
        },
        {
            "Id": 117,
            "Payee": "github",
            "TxnDate": "2025-08-27",
            "TotalAmt": 14.22,
            "LineDescription": "Github | **** 5590 – Isuru Koralage",
        },
        {"Id": 3, "Payee": "", "TxnDate": "2025-08-19", "TotalAmt": 35241.70, "LineDescription": ""},
        {"Id": 2, "Payee": "", "TxnDate": "2025-08-19", "TotalAmt": 70.34, "LineDescription": ""},
    ]

    statement_df = pd.DataFrame(statement_data)
    qb_df = pd.DataFrame(qb_data)

    print("=== Testing Improved Transaction Matching Algorithm ===\n")
    print("Statement Transactions:")
    print(statement_df[["tx_date", "description", "amount"]].to_string(index=False))
    print("\nQuickBooks Transactions:")
    print(qb_df[["Id", "Payee", "TxnDate", "TotalAmt", "LineDescription"]].to_string(index=False))

    # Create agent and test the matching
    agent = AccountingAPIAgent()
    chat_log = MockChatLog()

    print("\n=== Running Matching Algorithm ===")
    filtered_statement_df, filtered_qb_df = agent._match_transactions_by_amount_and_date(statement_df, qb_df, chat_log)

    print(f"\n=== Results ===")
    print(f"Matched {len(filtered_statement_df)} statement transactions with {len(filtered_qb_df)} QB transactions")

    if not filtered_statement_df.empty and not filtered_qb_df.empty:
        print("\nMatched Statement Transactions:")
        print(filtered_statement_df[["tx_date", "description", "amount"]].to_string(index=False))
        print("\nMatched QB Transactions:")
        print(filtered_qb_df[["Id", "Payee", "TxnDate", "TotalAmt", "LineDescription"]].to_string(index=False))

        # Show the pairing
        print("\n=== Transaction Pairings ===")
        for i in range(len(filtered_statement_df)):
            stmt_row = filtered_statement_df.iloc[i]
            qb_row = filtered_qb_df.iloc[i]
            print(f"Statement: {stmt_row['tx_date']} ${stmt_row['amount']:.2f} - {stmt_row['description'][:50]}...")
            print(
                f"QB:        {qb_row['TxnDate']} ${qb_row['TotalAmt']:.2f} - {qb_row['Payee']} - {qb_row['LineDescription'][:50]}..."
            )
            print()
    else:
        print("No matches found!")


if __name__ == "__main__":
    test_improved_matching()
