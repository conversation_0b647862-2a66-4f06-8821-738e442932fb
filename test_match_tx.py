import pandas as pd
from utils.metalake import load_data, intelligent_text_match
from datetime import timedelta

chat_id = "68ceaf155ab71dd5d7ccde5e"

# Load data
df_stmt = load_data(chat_id, "float_statement_2025-08-01_2025-08-31_transactions.dat")
df_qb = load_data(chat_id, "queried_purchase_records.dat")

if df_stmt.empty or df_qb.empty:
    print("No data found in one or both of the specified files.")
else:
    # --- Amount/date parsing helpers ---
    def parse_amount(val):
        if pd.isnull(val):
            return None
        s = str(val).replace("$", "").replace(",", "").strip()
        neg = s.startswith("(") and s.endswith(")")
        if neg:
            s = s[1:-1]
        try:
            num = float(s)
            return -num if neg else num
        except:
            return None

    # Prepare statement data
    df_stmt["amount_float"] = df_stmt["amount"].apply(parse_amount)
    df_stmt["tx_date_dt"] = pd.to_datetime(df_stmt["tx_date"], errors="coerce")
    # Prepare QB data
    qb_amt_col = "LineAmount" if "LineAmount" in df_qb.columns else "TotalAmt"
    df_qb["qb_amount_float"] = df_qb[qb_amt_col].apply(parse_amount)
    df_qb["qb_txn_date_dt"] = pd.to_datetime(df_qb["TxnDate"], errors="coerce")
    # For matching bookkeeping
    stmt_results = []
    qb_matched = set()
    # For each statement row, find a matching QB purchase
    for idx, s_row in df_stmt.iterrows():
        s_date = s_row["tx_date_dt"]
        s_amt = s_row["amount_float"]
        if pd.isnull(s_date) or pd.isnull(s_amt):
            stmt_results.append(
                {
                    "statement_tx_date": s_row["tx_date"],
                    "statement_description": s_row["description"],
                    "statement_amount": s_row["amount"],
                    "qb_transaction_id": "",
                    "qb_txn_date": "",
                    "qb_amount": "",
                    "match_status": "Unmatched",
                }
            )
            continue
        date_lo = s_date - timedelta(days=5)
        date_hi = s_date + timedelta(days=5)
        # Amount window: ±5% or $0.50, whichever is greater
        amt_tol = max(abs(s_amt * 0.05), 0.50)
        qb_cand = df_qb[(df_qb["qb_txn_date_dt"] >= date_lo) & (df_qb["qb_txn_date_dt"] <= date_hi)]
        qb_cand = qb_cand[
            (qb_cand["qb_amount_float"] >= s_amt - amt_tol) & (qb_cand["qb_amount_float"] <= s_amt + amt_tol)
        ]
        if not qb_cand.empty:
            # Use cognitive matcher
            match_cols = []
            if "LineDescription" in qb_cand.columns:
                match_cols.append("LineDescription")
            if "Payee" in qb_cand.columns:
                match_cols.append("Payee")
            # Defensive fallback
            if not match_cols:
                match_cols = [qb_amt_col]
            # Create a temp DataFrame, add statement description as a column for context
            qb_cand = qb_cand.copy().reset_index(drop=True)
            qb_cand["statement_description"] = s_row["description"]
            search_query = "Find the QB purchase that semantically matches the bank statement transaction's description, vendor, and nature of purchase."
            results = intelligent_text_match(chat_id, qb_cand, match_cols + ["statement_description"], search_query)
            if not results.empty:
                # Take best match (first row)
                best = results.iloc[0]
                qb_id = best["Id"] if "Id" in best else ""
                qb_txn_date = str(best["TxnDate"]) if "TxnDate" in best else ""
                qb_amt = best[qb_amt_col] if qb_amt_col in best else ""
                qb_amt_fmt = f"${float(qb_amt):,.2f}" if qb_amt != "" else ""
                stmt_results.append(
                    {
                        "statement_tx_date": s_row["tx_date"],
                        "statement_description": s_row["description"],
                        "statement_amount": s_row["amount"],
                        "qb_transaction_id": qb_id,
                        "qb_txn_date": qb_txn_date,
                        "qb_amount": qb_amt_fmt,
                        "match_status": "Matched",
                    }
                )
                qb_matched.add(qb_id)
                continue
        # No match found
        stmt_results.append(
            {
                "statement_tx_date": s_row["tx_date"],
                "statement_description": s_row["description"],
                "statement_amount": s_row["amount"],
                "qb_transaction_id": "",
                "qb_txn_date": "",
                "qb_amount": "",
                "match_status": "Unmatched",
            }
        )
    # Now add unmatched QB purchases
    for _, qb_row in df_qb.iterrows():
        qb_id = qb_row["Id"] if "Id" in qb_row else ""
        if qb_id not in qb_matched:
            qb_txn_date = qb_row["TxnDate"] if "TxnDate" in qb_row else ""
            qb_amt = qb_row[qb_amt_col] if qb_amt_col in qb_row else ""
            qb_amt_fmt = f"${float(qb_amt):,.2f}" if qb_amt != "" else ""
            stmt_results.append(
                {
                    "statement_tx_date": "",
                    "statement_description": "",
                    "statement_amount": "",
                    "qb_transaction_id": qb_id,
                    "qb_txn_date": qb_txn_date,
                    "qb_amount": qb_amt_fmt,
                    "match_status": "Unmatched",
                }
            )
    # Output DataFrame
    out_cols = [
        "statement_tx_date",
        "statement_description",
        "statement_amount",
        "qb_transaction_id",
        "qb_txn_date",
        "qb_amount",
        "match_status",
    ]
    out_df = pd.DataFrame(stmt_results, columns=out_cols)
    out_df.to_csv("files/statement_qb_matches.csv", index=False)
    # Summary markdown preview
    summary = out_df["match_status"].value_counts().reset_index()
    summary.columns = ["match_status", "count"]
    print("\n### Match Summary\n" + summary.to_markdown(index=False))
