"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class AccountingApiAgent
* @description Tool responsible for handling QuickBooks API to perform accounting tasks.
* <AUTHOR>
"""

from datetime import datetime, timed<PERSON><PERSON>
import json
from logging import Logger
import os
import re
import traceback
from zoneinfo import ZoneInfo
import pandas as pd
import xxhash
from utils.logger import get_debug_logger
from models.conversation import Conversation
from services.tools.data_processor.data_processor_agent import DataProcessorAgent
from utils.constant import AgentState, AnalysisStep
from utils.metalake import (
    intelligent_text_match,
    cognitive_list_match,
    load_data,
    quickbooks_create_bill,
    quickbooks_create_vendor,
    quickbooks_update_vendor,
    save_data,
    quickbooks_create_purchase,
    quickbooks_upload_attachment,
    quickbooks_query_object,
    quickbooks_update_purchase,
    quickbooks_get_company_currency,
)

from services.shared.python_code_agent import AccountingApiCodeAgent
from utils.misc_utils import convert_currency, get_table_headers_and_rows_from_csv, csv_to_markdown
from services.tools.base_tool import ToolOutput


class AccountingApiAgent(DataProcessorAgent):
    """
    Agentic Python workflow agent that manages the state transitions for solving a user question using Python code, code review, execution, and data review.
    Input: user question + data file names list
    """

    def __init__(self):
        super().__init__()
        # Register state handler classes
        self.state_handler_class_map[AgentState.DATA_PROCESSING] = AccountingApiCodeAgent
        self.company_currency = None
        # Load company currency from QuickBooks
        self.company_currency = quickbooks_get_company_currency()

    """
    additional_data contains the following:
    1. api_task: The task to perform. Eg: add_expense, add_bill, add_vendor
    """

    def execute_task(
        self,
        conversation: Conversation,
        data_source_name: str,
        data_file_name_list: list,
        task_instruction: str,
        base_log_path: str = "",
        additional_data: dict = None,
        event_loop=None,
    ):
        conversation.active_session_send_analysis_step(AnalysisStep.ACCOUNTING_API)
        chat_log = get_debug_logger(
            f"chat_{conversation.chat_id}", f"./logs/{base_log_path}chat_{conversation.chat_id}.log"
        )
        # Load first 5 records from given data files (CSV) and give as additional input to Python code agent
        # First check the existance of all data files and inform back if any of them are missing
        non_existing_files_list = []
        data_file_path_list = []
        for data_file_name in data_file_name_list:
            data_file_path = f"storage/public/{conversation.chat_id}/files/{data_file_name}"
            if not os.path.exists(data_file_path):
                # Now check if the file exists in attachments folder
                data_file_path = f"storage/public/{conversation.chat_id}/attachments/{data_file_name}"
                if not os.path.exists(data_file_path):
                    non_existing_files_list.append(data_file_name)
                else:
                    data_file_path_list.append(data_file_path)
            else:
                data_file_path_list.append(data_file_path)
        if non_existing_files_list:
            return ToolOutput(
                display_output=f"Failed to find following mentioned files: {non_existing_files_list}\n\nPlease check the file names and try again.\n\n",
                result_text_list=[tuple([f"Files not found: {non_existing_files_list}", False])],
            )

        # If this is one of the defined tasks, then call the pre-defined function
        output_dict = {}
        task_executed_with_predefined_function = False
        if additional_data and "api_task" in additional_data:
            api_task = additional_data["api_task"]
            if api_task == "add_expenses":
                output_dict = self.add_expenses(
                    conversation, data_source_name, data_file_path_list, task_instruction, chat_log
                )
                task_executed_with_predefined_function = True
            elif api_task == "add_bills":
                output_dict = self.add_bills(
                    conversation, data_source_name, data_file_path_list, task_instruction, chat_log
                )
                task_executed_with_predefined_function = True
            elif api_task == "update_expenses":
                output_dict = self.update_expenses(
                    conversation, data_source_name, data_file_path_list, task_instruction, chat_log
                )
                task_executed_with_predefined_function = True
            # elif api_task == "update_expense_lines":
            #     output_dict = self.update_expense_lines(conversation, data_file_path_list, task_instruction, chat_log)
            #     task_executed_with_predefined_function = True

            elif api_task == "query_transactions":
                # Get the transaction type from the "data_type_list" in additional_data
                if "data_type_list" not in additional_data or not additional_data["data_type_list"]:
                    return ToolOutput(
                        display_output="Failed to find data_type_list in additional_data. Please check the additional_data and try again.",
                        result_text_list=[
                            (
                                "Failed to find data_type_list in additional_data. Please check the additional_data and try again.",
                                False,
                            )
                        ],
                    )
                # Get from_date and to_date from additional_data
                if "from_date" not in additional_data or "to_date" not in additional_data:
                    return ToolOutput(
                        display_output="Failed to find from_date or to_date in additional_data. Please check the additional_data and try again.",
                        result_text_list=[
                            (
                                "Failed to find from_date or to_date in additional_data. Please check the additional_data and try again.",
                                False,
                            )
                        ],
                    )
                transaction_type = additional_data["data_type_list"][0]
                from_date = additional_data["from_date"]
                to_date = additional_data["to_date"]
                output_dict = self.query_transactions(
                    conversation, transaction_type, chat_log, from_date=from_date, to_date=to_date
                )
                task_executed_with_predefined_function = True
            elif api_task == "match_statement":
                # Get entity type from data_type_list in additional_data
                if "data_type_list" not in additional_data or not additional_data["data_type_list"]:
                    return ToolOutput(
                        display_output="Failed to find data_type_list.",
                        result_text_list=[
                            (
                                "Failed to find data_type_list.",
                                False,
                            )
                        ],
                    )
                entity_type = additional_data["data_type_list"][0]
                output_dict = self.match_statement(conversation, data_file_path_list, entity_type, chat_log)
                task_executed_with_predefined_function = True

        if not task_executed_with_predefined_function:
            # Go through each CSV file and convert them to .dat file and get preview data
            preview_data_str = ""
            for data_file_path in data_file_path_list:
                data_file_name = data_file_path.split("/")[-1]
                # Get preview only from .csv
                if not data_file_path.endswith(".csv"):
                    continue
                # headers, rows = get_table_headers_and_rows_from_csv(data_file_path, 5, chat_log)
                # # Save to .dat using save_data - first merge headers and rows
                # data_records = [headers] + [rows]
                # save_data(conversation.chat_id, data_file_name, data_records)
                markdown_data, truncated, row_count = csv_to_markdown(data_file_path, 10)
                preview_data_str += (
                    "\n\nData file name: " + data_file_name + ": First 10 rows:\n\n" + markdown_data + "\n\n"
                )

            input_data = {
                "user_question": task_instruction,
                "data_file_names": data_file_name_list,
                "data_input": preview_data_str,
            }
            # Set the correct agent state to start with
            conversation.agent_state = AgentState.DATA_PROCESSING
            output_dict = self.start(
                conversation,
                data_source_name,
                input_data,
                base_log_path,
            )

        tool_result_to_summarize = ""
        tool_result_to_not_summarize = ""
        # Get output CSV file names
        csv_file_names = []
        data_file_names = []
        output_file_preview_str = ""
        for file_path in output_dict.get("csv_paths", []):
            # check if the extension is csv, if not then skip
            if not file_path.endswith(".csv"):
                continue
            csv_file_names.append(file_path.split("/")[-1])
            markdown_data, truncated, row_count = csv_to_markdown(file_path, 10)
            output_file_preview_str += (
                "\n\nOutput file name: "
                + str(file_path.split("/")[-1])
                + ": First 10 rows:\n\n"
                + markdown_data
                + "\n\n"
            )
            # Update .dat file name list if exists
            data_file_name = file_path.split("/")[-1].replace(".csv", ".dat")
            if os.path.exists(f"storage/public/{conversation.chat_id}/{data_file_name}.pkl"):
                data_file_names.append(data_file_name)

        if "result_markdown" in output_dict:
            tool_result_to_summarize += "Result:\n\n" + output_dict["result_markdown"] + "\n\n"
            tool_result_to_not_summarize += "Result:\n\n" + output_dict["result_markdown"] + "\n\n"
        if output_dict.get("python_code", ""):
            tool_result_to_summarize += "Python code:\n\n```python\n" + output_dict["python_code"] + "\n````\n\n"

        tool_result_to_summarize += (
            output_dict.get("execution_result", "") + "\n\nOutput csv files:\n\n" + output_file_preview_str
        )
        if data_file_names:
            tool_result_to_summarize += "\n\nOutput data files: " + str(data_file_names) + "\n\n"

        tool_result_to_not_summarize += (
            output_dict.get("execution_result", "") + "\n\n" + "Output csv files: " + str(csv_file_names)
        )
        if data_file_names:
            tool_result_to_not_summarize += "\n\nOutput data files: " + str(data_file_names) + "\n\n"

        tool_results_list = [
            tuple([tool_result_to_summarize, True]),
            tuple([tool_result_to_not_summarize, False]),
        ]

        # output_display = (
        #     "Result: " + output_dict.get("execution_result", "") + "\n\n" + "Output CSV files: " + str(csv_file_names)
        # )
        return ToolOutput(display_output=tool_result_to_summarize, result_text_list=tool_results_list)

    # Pre-defined functions
    """
    Description: Add list of expenses to QuickBooks
    Input: CSV file with following columns:
    1. Date
    2. Vendor
    3. Account
    4. Amount
    5. Memo
    6. Attachments (optional)
    Output: List of csv files with reported status of each expense recording
    """

    def add_expenses(
        self, conversation: Conversation, source: str, data_file_path_list: list, task_instruction, chat_log
    ):
        try:
            # First validate whether purchases.csv and purchase_lines.csv are present in the data_file_path_list
            purchases_csv_path = None
            purchase_lines_csv_path = None
            for data_file_path in data_file_path_list:
                if data_file_path.endswith("purchases.csv"):
                    purchases_csv_path = data_file_path
                elif data_file_path.endswith("purchase_lines.csv"):
                    purchase_lines_csv_path = data_file_path

            if not purchases_csv_path:
                return {
                    "execution_result": "Failed to find purchases.csv file. Please check the file names and try again.",
                    "csv_paths": [],
                }
            if not purchase_lines_csv_path:
                return {
                    "execution_result": "Failed to find purchase_lines.csv file. Please check the file names and try again.",
                    "csv_paths": [],
                }

            return self._add_entities(
                conversation,
                "Purchase",
                source,
                purchases_csv_path,
                purchase_lines_csv_path,
                task_instruction,
                chat_log,
            )
        except Exception as e:
            chat_log.error(f"Error adding expenses: {e}: {traceback.format_exc()}")
            return {
                "execution_result": f"Failed to add expenses. Error: {e}",
                "csv_paths": [],
            }

    """
    Description: Add list of bills to QuickBooks
    Input: CSV files with following columns:
    1. Date
    2. Vendor
    3. AP Account
    4. Line Amounts
    5. Line Expense Accounts
    6. Line Tax Codes
    7. Attachments (optional)
    8. Term (optional)
    9. Due Date (optional)
    Output: List of csv files with reported status of each bill recording
    """

    def add_bills(
        self, conversation: Conversation, source: str, data_file_path_list: list, task_instruction, chat_log
    ):
        try:
            # First validate whether bills.csv and bill_lines.csv are present in the data_file_path_list
            bills_csv_path = None
            bill_lines_csv_path = None
            for data_file_path in data_file_path_list:
                if data_file_path.endswith("bills.csv"):
                    bills_csv_path = data_file_path
                elif data_file_path.endswith("bill_lines.csv"):
                    bill_lines_csv_path = data_file_path
            if not bills_csv_path:
                return {
                    "execution_result": "Failed to find bills.csv file. Please check the file names and try again.",
                    "csv_paths": [],
                }
            if not bill_lines_csv_path:
                return {
                    "execution_result": "Failed to find bill_lines.csv file. Please check the file names and try again.",
                    "csv_paths": [],
                }
            return self._add_entities(
                conversation, "Bill", source, bills_csv_path, bill_lines_csv_path, task_instruction, chat_log
            )
        except Exception as e:
            chat_log.error(f"Error adding bills: {e}: {traceback.format_exc()}")
            return {
                "execution_result": f"Failed to add bills. Error: {e}",
                "csv_paths": [],
            }

    """
    Description : Function to match bank statement with QuickBooks transactions
    """

    def match_statement(self, conversation: Conversation, data_file_path_list: list, entity_type: str, chat_log):
        # Validate whether a CSV file is provided with data_file_path_list
        statement_csv_path = None
        for data_file_path in data_file_path_list:
            if data_file_path.endswith(".csv"):
                statement_csv_path = data_file_path
        if not statement_csv_path:
            return {
                "execution_result": "Failed to find CSV file with statement transactions. Please check the file names and try again.",
                "csv_paths": [],
            }

        # Currently on 'Purchase' entity type is supported
        if entity_type != "Purchase":
            return {
                "execution_result": "Invalid entity type. Currently only 'Purchase' is supported.",
                "csv_paths": [],
            }

        try:
            # Determine statement end date and start date from tx_date column
            statement_df = pd.read_csv(statement_csv_path)
            # Add index column for tracking matches
            statement_df["index"] = statement_df.index
            if "tx_date" not in statement_df.columns:
                return {
                    "execution_result": "Failed to find tx_date column in the CSV file. Please check the file and try again.",
                    "csv_paths": [],
                }
            statement_end_date = statement_df["tx_date"].max()
            statement_start_date = statement_df["tx_date"].min()

            # Ensure statement_end_date is in string format
            if not isinstance(statement_end_date, str):
                statement_end_date = statement_end_date.strftime("%Y-%m-%d")

            # Add 5 days buffer to the start date
            # Convert string to datetime, subtract timedelta, then convert back to string
            if isinstance(statement_start_date, str):
                try:
                    start_date_obj = datetime.strptime(statement_start_date, "%Y-%m-%d")
                    start_date_obj = start_date_obj - timedelta(days=5)
                    statement_start_date = start_date_obj.strftime("%Y-%m-%d")
                except ValueError:
                    # If the date format is different, try other common formats
                    try:
                        start_date_obj = datetime.strptime(statement_start_date, "%Y-%m-%d %H:%M:%S")
                        start_date_obj = start_date_obj - timedelta(days=5)
                        statement_start_date = start_date_obj.strftime("%Y-%m-%d")
                    except ValueError:
                        chat_log.error(f"Unable to parse statement_start_date: {statement_start_date}")
                        return {
                            "execution_result": f"Failed to parse statement start date: {statement_start_date}. Expected format: YYYY-MM-DD",
                            "csv_paths": [],
                        }
            else:
                # If it's already a datetime object, subtract directly and convert to string
                statement_start_date = (statement_start_date - timedelta(days=5)).strftime("%Y-%m-%d")

            # Check if the statement amount column has valid numeric values
            if "amount" not in statement_df.columns:
                return {
                    "execution_result": "Failed to find amount column in the CSV file. Please check the file and try again.",
                    "csv_paths": [],
                }
            try:
                statement_df["amount"] = statement_df["amount"].apply(lambda x: float(x))
            except ValueError:
                return {
                    "execution_result": "Failed to convert amount column to numeric values. Please check the file and try again.",
                    "csv_paths": [],
                }

            # Statement data pre-processing - turn the negative amounts to positive amounts
            statement_df["amount"] = statement_df["amount"].apply(lambda x: abs(x))

            # Query transactions from QuickBooks for the date range
            query_result = self.query_transactions(
                conversation,
                entity_type,
                chat_log,
                from_date=statement_start_date,
                to_date=statement_end_date,
                is_combine_line_items=True,
            )

            qb_transactions_df = query_result.get("records_df")
            if qb_transactions_df.empty:
                return {
                    "execution_result": "No transactions found in the relevant date range.",
                    "csv_paths": [],
                }

            # Call the transaction matching sub-function to filter transactions by amount and date
            filtered_statement_df, filtered_qb_df = self._match_transactions_by_amount_and_date(
                statement_df, qb_transactions_df, chat_log
            )
            if filtered_statement_df.empty or filtered_qb_df.empty:
                chat_log.info(
                    "No matching transactions found between statement and QuickBooks data after matching by transaction amount and date."
                )
                return {
                    "execution_result": "No matching transactions found between statement and QuickBooks data after matching by transaction amount and date.",
                    "csv_paths": [],
                }
            chat_log.info(
                f"{filtered_statement_df.shape[0]} matching transactions found between statement and QuickBooks data after matching by transaction amount and date."
            )

            # Copy the columns in 'filtered_statement_df' to make compatible with 'filtered_qb_df'
            filtered_statement_df["TxnDate"] = filtered_statement_df["tx_date"]
            filtered_statement_df["TotalAmt"] = filtered_statement_df["amount"]
            filtered_statement_df["Payee"] = filtered_statement_df["description"]
            filtered_statement_df["LineDescription"] = filtered_statement_df["description"]

            # For each tx in filtered_statement_df, find a matching tx in filtered_qb_df based on amount and date using coginitive match
            matching_instruction = "Lookup the QuickBooks transaction list to find the matching record that corresponds to the given statement transaction by comparing the transaction date (TxnDate), total amount (TotalAmt), vendor(Payee), and line item descriptions (LineDescription).\n\
            1. Intelligently match the vendor and line item descriptions first. Note that same vendor can have different names in the statement and QuickBooks.\n\
            2. If there is no matching for vendor and line item descriptions, then return none. \n\
            3. If there are multiple matches, choose the one which has the closest transaction date and the total amount."

            # Add match_index column to QB DataFrame for tracking matches
            filtered_qb_df_with_index = filtered_qb_df.copy()
            filtered_qb_df_with_index["match_index"] = filtered_qb_df_with_index.index

            matched_df = cognitive_list_match(
                conversation.chat_id,
                filtered_statement_df,
                filtered_qb_df_with_index,
                ["TxnDate", "TotalAmt", "Payee", "LineDescription"],
                matching_instruction,
            )
            if matched_df.empty:  # No matches found
                chat_log.info(
                    "No matching transactions found between statement and QuickBooks data after cognitive match."
                )
                return {
                    "execution_result": "No matching transactions found between statement and QuickBooks data after cognitive match.",
                    "csv_paths": [],
                }
            chat_log.info(
                f"{matched_df.shape[0]} matching transactions found between statement and QuickBooks data after cognitive match."
            )

            # Create output DataFrame from matched transactions
            stmt_results = []
            num_matches = 0

            # For each transaction in statement output the match result, matched QB transaction id, QB transaction date, QB payee, QB amount, QB line item descriptions (separated by comma)
            for _, s_row in statement_df.iterrows():
                match_row = matched_df[matched_df["index"] == s_row.name]
                if not match_row.empty:
                    # match_idx = match_row["match_index"].values[0]
                    # match_row = filtered_qb_df.iloc[match_idx]
                    qb_id = match_row["Id"].iloc[0] if "Id" in match_row.columns else ""
                    qb_txn_date = (
                        match_row["qb_txn_date_dt"].iloc[0]
                        if "qb_txn_date_dt" in match_row.columns
                        else match_row["TxnDate"].iloc[0]
                    )
                    qb_amt = match_row["TotalAmt"].iloc[0] if "TotalAmt" in match_row.columns else ""
                    qb_payee = match_row["Payee"].iloc[0] if "Payee" in match_row.columns else ""
                    qb_line_descriptions = (
                        match_row["LineDescription"].iloc[0] if "LineDescription" in match_row.columns else ""
                    )
                    stmt_results.append(
                        {
                            "statement_tx_date": s_row["tx_date"],
                            "statement_description": s_row["description"],
                            "statement_amount": s_row["amount"],
                            "match_status": "Matched",
                            "qb_transaction_id": qb_id,
                            "qb_txn_date": qb_txn_date,
                            "qb_payee": qb_payee,
                            "qb_amount": qb_amt,
                            "qb_line_descriptions": qb_line_descriptions,
                        }
                    )
                    num_matches += 1
                    continue
                stmt_results.append(
                    {
                        "statement_tx_date": s_row["tx_date"],
                        "statement_description": s_row["description"],
                        "statement_amount": s_row["amount"],
                        "match_status": "Unmatched",
                        "qb_transaction_id": "",
                        "qb_txn_date": "",
                        "qb_payee": "",
                        "qb_amount": "",
                        "qb_line_descriptions": "",
                    }
                )

            # Create output DataFrame
            out_cols = [
                "statement_tx_date",
                "statement_description",
                "statement_amount",
                "match_status",
                "qb_transaction_id",
                "qb_txn_date",
                "qb_payee",
                "qb_amount",
                "qb_line_descriptions",
            ]
            out_df = pd.DataFrame(stmt_results, columns=out_cols)

            # Save results to CSV
            file_name = f"statement_qb_matches.csv"
            out_path = f"storage/public/{conversation.chat_id}/files/{file_name}"
            out_df.to_csv(out_path, index=False)
            conversation.data_reference_set.add(file_name)
            # Create dat file 'statement_qb_matches.dat'
            dat_file_name = file_name.replace(".csv", ".dat")
            parsed_records = out_df.to_dict("records")
            save_data(conversation.chat_id, dat_file_name, parsed_records)
            conversation.data_reference_set.add(dat_file_name)

            # Generate summary statistics
            summary_markdown = f"| Statement Records | Total Purchases in QB from {statement_start_date} to {statement_end_date} | Matched | Unmatched |\n|---|---|---|---|\n| {statement_df.shape[0]} | {filtered_qb_df.shape[0]} | {num_matches} | {statement_df.shape[0] - num_matches} |"

            # Generate detailed results markdown
            results_markdown = out_df.head(20).to_markdown(index=False)

            chat_log.info(f"Transaction matching completed. Found {num_matches} matches. Results saved to {file_name}")

            return {
                "execution_result": f"Transaction matching completed successfully.\n\n### Match Summary\n{summary_markdown}\n\n### Sample Results (first 20 rows)\n{results_markdown}\n\nFull results saved to {file_name}",
                "csv_paths": [out_path],
            }
        except Exception as e:
            chat_log.error(f"Error matching transactions: {str(e)}: \n{traceback.format_exc()}")
            return {
                "execution_result": f"Error matching transactions: {str(e)}",
                "csv_paths": [],
            }

    def _match_transactions_by_amount_and_date(
        self, statement_df: pd.DataFrame, qb_transactions_df: pd.DataFrame, chat_log
    ):
        """
        Match bank statement transactions with QuickBooks transactions by amount and date.

        Args:
            statement_df: DataFrame containing bank statement transactions
            qb_transactions_df: DataFrame containing QuickBooks transactions
            chat_log: Logger instance

        Returns:
            Tuple of (filtered_statement_df, filtered_qb_df) containing only matched transactions
        """
        # Validate required columns in statement data
        required_stmt_cols = ["tx_date", "amount"]
        missing_cols = [col for col in required_stmt_cols if col not in statement_df.columns]
        if missing_cols:
            chat_log.error(f"Missing required columns in statement: {missing_cols}")
            return pd.DataFrame(), pd.DataFrame()

        # Prepare statement data
        statement_df = statement_df.copy()
        statement_df["tx_date_dt"] = pd.to_datetime(statement_df["tx_date"], errors="coerce")

        # Prepare QuickBooks data
        qb_transactions_df = qb_transactions_df.copy()
        qb_transactions_df["qb_txn_date_dt"] = pd.to_datetime(qb_transactions_df["TxnDate"], errors="coerce")

        matched_statement_indices = []
        matched_qb_indices = []
        qb_matched = set()

        chat_log.info(
            f"Starting to match {len(statement_df)} statement transactions with {len(qb_transactions_df)} QuickBooks transactions"
        )

        # For each statement row, find a matching QB purchase
        for idx, s_row in statement_df.iterrows():
            s_date = s_row["tx_date_dt"]
            s_amt = pd.to_numeric(s_row["amount"], errors="coerce")

            if pd.isnull(s_date) or pd.isnull(s_amt):
                continue

            # Date range: ±5 days from statement date
            date_lo = s_date - pd.Timedelta(days=5)
            date_hi = s_date + pd.Timedelta(days=5)

            # Amount tolerance: ±5% or $0.50, whichever is greater
            amt_tol = max(abs(s_amt * 0.05), 0.50)

            # Filter QB transactions by date and amount
            qb_candidates = qb_transactions_df[
                (qb_transactions_df["qb_txn_date_dt"] >= date_lo) & (qb_transactions_df["qb_txn_date_dt"] <= date_hi)
            ]

            # Convert QB amounts to numeric for comparison
            qb_candidates = qb_candidates.copy()
            qb_candidates["qb_amount_numeric"] = pd.to_numeric(qb_candidates["TotalAmt"], errors="coerce")

            qb_candidates = qb_candidates[
                (qb_candidates["qb_amount_numeric"] >= s_amt - amt_tol)
                & (qb_candidates["qb_amount_numeric"] <= s_amt + amt_tol)
            ]

            # Remove already matched QB transactions
            qb_candidates = qb_candidates[~qb_candidates["Id"].isin(qb_matched)]

            if not qb_candidates.empty:
                # Take the first match (or implement more sophisticated matching logic here if needed)
                best_match = qb_candidates.iloc[0]

                # Record the matched indices
                matched_statement_indices.append(idx)
                matched_qb_indices.append(best_match.name)
                qb_matched.add(best_match["Id"])

        # Filter the original DataFrames to return only matched transactions
        filtered_statement_df = (
            statement_df.loc[matched_statement_indices].copy() if matched_statement_indices else pd.DataFrame()
        )
        filtered_qb_df = qb_transactions_df.loc[matched_qb_indices].copy() if matched_qb_indices else pd.DataFrame()

        chat_log.info(
            f"Found {len(matched_statement_indices)} matching statement transactions and {len(matched_qb_indices)} matching QB transactions"
        )
        return filtered_statement_df, filtered_qb_df

    """
    Generic function to add entities to QuickBooks
    """

    def _add_entities(
        self,
        conversation: Conversation,
        entity_type: str,
        source: str,
        header_file_path: str,
        line_item_file_path: str,
        task_instruction,
        chat_log,
    ):
        # Validate source - should be either 'invoice', 'receipt' or 'statement'
        if source not in ["invoice", "receipt", "statement"]:
            return {
                "execution_result": "Invalid source. Should be either 'invoice', 'receipt' or 'statement'.",
                "csv_paths": [],
            }

        chat_id = conversation.chat_id

        # --- Load Data ---
        entity_type_lower = entity_type.lower()
        try:
            entities_df = load_data(chat_id, f"{entity_type_lower}s.csv")
        except Exception as e:
            entities_df = pd.DataFrame()
            load_entities_err = str(e)

        try:
            lines_df = load_data(chat_id, f"{entity_type_lower}_lines.csv")
        except Exception as e:
            lines_df = pd.DataFrame()
            load_lines_err = str(e)

        results = []
        created_ids = []
        updated_ids = []
        n_duplicates = 0
        n_updates = 0
        # Track the first created at time - in local timezone - in string format: YYYY-MM-DDTHH:MM:SS-HH:MM
        company_tz = ZoneInfo("America/Winnipeg")
        local_time = datetime.now().astimezone(company_tz)
        # Format in ISO 8601 with offset
        first_created_cutoff = local_time.isoformat()

        csv_output_file_paths = []

        if entities_df.empty:
            # No entities to process
            # Output empty result with explanation
            out_path = f"files/results_add_{entity_type_lower}s.csv"
            pd.DataFrame(
                [
                    {
                        f"{entity_type_lower}_key": None,
                        f"{entity_type_lower}_id": None,
                        "status": "Failed",
                        "error": f"No {entity_type_lower}s to process.",
                        "attachment_files_attempted": 0,
                        "attachment_success_count": 0,
                        "attachment_error_count": 0,
                    }
                ]
            ).to_csv(f"storage/public/{chat_id}/{out_path}", index=False)
            chat_log.info(f"0 success, 0 failed — see results_add_{entity_type_lower}s.csv")
            return {
                "execution_result": f"0 success, 0 failed — see results_add_{entity_type_lower}s.csv",
                "csv_paths": [f"storage/public/{chat_id}/files/results_add_{entity_type_lower}s.csv"],
            }

        # For auditability
        entities_df = entities_df.sort_values(f"{entity_type_lower}_key")
        if not lines_df.empty:
            lines_df = lines_df.sort_values([f"{entity_type_lower}_key", "line_number"])
        else:
            # No lines for any entities
            lines_df = pd.DataFrame(
                columns=[
                    f"{entity_type_lower}_key",
                    "line_number",
                    "detail_type",
                    "line_amount",
                    "description",
                    "expense_account_id",
                    "tax_code_id",
                ]
            )

        for _, row in entities_df.iterrows():
            # --- Build payload ---
            payload_res = self._quickbooks_create_entity_payload(source, row, lines_df, chat_id, chat_log, entity_type)
            if not payload_res.get("is_success"):
                results.append(
                    {
                        f"{entity_type_lower}_key": row.get(f"{entity_type_lower}_key"),
                        f"{entity_type_lower}_id": None,
                        "status": "Data validation failed",
                        "success": False,
                        "error": payload_res.get("error_message", "Unknown error"),
                        "attachment_files_attempted": 0,
                        "attachment_success_count": 0,
                        "attachment_error_count": 0,
                    }
                )
                continue
            payload = payload_res.get("payload")
            entity_key = row.get(f"{entity_type_lower}_key")
            attachment_file_name = row.get("attachment_file_name", "")
            attachment_needed = attachment_file_name not in [None, "", "N/A", "n/a"]
            attach_attempted = 0
            attach_success = 0
            attach_error = 0
            err = ""
            created_or_updated_id = ""
            # --- Find matching existing entity if any ---
            existing_entity = self._find_matched_existing_item(
                entity_type, payload, source == "statement", created_ids, chat_id, chat_log
            )
            if existing_entity is not None:
                res = self._quickbooks_update_item_with_existing(
                    entity_type, source, entity_key, payload, existing_entity, chat_log
                )
                if not res.get("success"):
                    err = res.get("message", "Unknown error")
                if res["success"]:
                    created_or_updated_id = res["createdId"]
                    if res["is_update_needed"]:
                        updated_ids.append(res["createdId"])
                        n_updates += 1
                    else:
                        n_duplicates += 1
                else:
                    chat_log.error(
                        f"Failed to update existing {entity_type_lower} for {entity_type_lower} {entity_key}: {err}"
                    )
                    res["status"] = f"Failed to update existing {entity_type_lower}"
                # Attachment not needed if entity is already added via a receipt
                if "source=receipt" in existing_entity.get("PrivateNote", ""):
                    attachment_needed = False
            else:
                # --- Create Entity with QuickBooks API ---
                try:
                    # Remove original_amount and TotalAmt from the payload if exists (original amount in Line)
                    payload = {k: v for k, v in payload.items() if k not in ["TotalAmt", "converted_total_amount"]}
                    for line in payload["Line"]:
                        del line["original_amount"]
                    res = self._quickbooks_create_item(entity_type, payload, chat_log)
                except Exception as e:
                    res = {"success": False, "message": str(e)}
                if res.get("success"):
                    created_or_updated_id = res.get("createdId")
                    res["status"] = f"{entity_type} created successfully"
                    created_ids.append(created_or_updated_id)
                    chat_log.debug(
                        f"Created new {entity_type_lower} {created_or_updated_id} for {entity_type_lower} {entity_key}"
                    )
                else:
                    res["status"] = f"{entity_type} creation failed"
                    err = (str(err) + "; " if err else "") + f"{entity_type} creation failed: {res.get('message')}"

            # --- Upload Attachment if needed ---
            if attachment_needed and created_or_updated_id:
                chat_log.debug(
                    f"Uploading attachment {attachment_file_name} for {entity_type_lower} {entity_key} with id {created_or_updated_id}"
                )
                attach_attempted = 1
                try:
                    upload_res = quickbooks_upload_attachment(
                        chat_id, attachment_file_name, entity_type, created_or_updated_id
                    )
                    if upload_res.get("success"):
                        attach_success = 1
                    else:
                        attach_error = 1
                        err = (
                            str(err) + "; " if err else ""
                        ) + f"Attachment: {upload_res.get('message','Unknown error')}"
                except Exception as e:
                    attach_error = 1
                    err = (
                        str(err) + "; " if err else ""
                    ) + f"Attachment exception: {str(e)}: \n{traceback.format_exc()}"

            results.append(
                {
                    f"{entity_type_lower}_key": entity_key,
                    f"{entity_type_lower}_id": created_or_updated_id,
                    "status": res["status"],
                    "success": res.get("success"),
                    "error": res["message"] if not res.get("success") and "message" in res else err,
                    "attachment_files_attempted": attach_attempted,
                    "attachment_success_count": attach_success,
                    "attachment_error_count": attach_error,
                }
            )

        # Output results CSV
        file_name = f"results_add_{entity_type_lower}s.csv"
        out_path = f"files/{file_name}"
        # full_path = f"storage/public/{chat_id}/{out_path}"
        results_df = pd.DataFrame(results)
        # chat_log.info(f"Results file created at {full_path}:\n{results_df.head(5)}")
        # results_df.to_csv(full_path, index=False)
        # csv_output_file_paths.append(full_path)
        # Add to the conversation data reference set
        # conversation.data_reference_set.add(file_name)
        result_markdown = results_df.to_markdown(index=False)

        # Review created and updated entries
        created_entries_file_name, n_verified = self.review_created_entries(
            chat_id, entity_type, first_created_cutoff, created_ids, chat_log
        )
        if created_entries_file_name:
            conversation.data_reference_set.add(created_entries_file_name)
            csv_output_file_paths.append(f"storage/public/{chat_id}/files/{created_entries_file_name}")
        updated_entries_file_name, n_update_verified = self.review_updated_entries(
            chat_id, entity_type, first_created_cutoff, updated_ids, chat_log
        )
        n_verified += n_update_verified
        if updated_entries_file_name:
            conversation.data_reference_set.add(updated_entries_file_name)
            csv_output_file_paths.append(f"storage/public/{chat_id}/files/{updated_entries_file_name}")
        # Print summary
        n_success = sum(r["success"] for r in results)
        n_failed = sum(not r["success"] for r in results)
        chat_log.info(
            f"{n_success} success, {n_failed} failed, {n_verified} verified line items, {n_duplicates} duplicates, {n_updates} updates, result summary: \n{result_markdown}"
        )

        return {
            "execution_result": (
                f"{n_success} success, {n_failed} failed, {n_duplicates} duplicates, {n_updates} updates, {entity_type_lower} addition summary: \n{result_markdown} \n{n_verified} verified line items in QBO.\n\nQuickBooks Home Currency: {self.company_currency}"
                if created_entries_file_name or updated_entries_file_name
                else "Verification of created entries failed."
            ),
            "result_markdown": result_markdown,
            "csv_paths": csv_output_file_paths,
        }

    """
    Description: Query transactions from QuickBooks
    Input: 
    1. transaction_type: Type of the transaction to query (e.g. Bill, Purchase, JournalEntry, Transfer, etc.)
    2. from_date: Start date of the transaction date filter
    3. to_date: End date of the transaction date filter
    Output: CSV file with the queried transactions
    """

    def query_transactions(
        self,
        conversation: Conversation,
        transaction_type: str,
        chat_log: Logger,
        id_list: list = [],
        from_date: str = "",
        to_date: str = "",
        is_combine_line_items: bool = False,
    ):
        # Validate transaction type (currently only 'Purchase' is supported)
        if transaction_type != "Purchase":
            return {
                "execution_result": "Invalid transaction type. Currently only 'Purchase' is supported.",
                "csv_paths": [],
                "records": {},
                "records_df": pd.DataFrame(),
            }
        query = ""
        if id_list or from_date or to_date:
            query += "WHERE "
            if id_list:
                # Make sure id list is string list
                id_list = [("'" + str(i) + "'") for i in id_list]
                query += f"Id IN ({','.join(id_list)})"
            if from_date and to_date:
                if id_list:
                    query += " AND "
                query += f"TxnDate >= '{from_date}' AND TxnDate <= '{to_date}'"

        res = quickbooks_query_object(transaction_type, query)
        if not res.get("success"):
            return {
                "execution_result": f"Failed to query transactions. Error: {res.get('message')}",
                "csv_paths": [],
                "records": {},
                "records_df": pd.DataFrame(),
            }
        # Write the data records to a CSV file
        data_records = res["data"].get("QueryResponse", {}).get(transaction_type, [])
        if not data_records:
            return {
                "execution_result": "No transactions found for the given date range.",
                "csv_paths": [],
                "records": {},
                "records_df": pd.DataFrame(),
            }
        df = self._parse_created_entries(transaction_type, data_records, chat_log, is_combine_line_items)
        file_name = f"queried_{transaction_type.lower()}_records.csv"
        out_path = f"storage/public/{conversation.chat_id}/files/{file_name}"
        df.to_csv(out_path, index=False)
        # Write parsed records to .dat file as well
        dat_file_name = file_name.replace(".csv", ".dat")
        parsed_records = df.to_dict("records")
        save_data(conversation.chat_id, dat_file_name, parsed_records)
        conversation.data_reference_set.add(file_name)
        conversation.data_reference_set.add(dat_file_name)
        # Build dictionary with id as key and record as value
        records_dict = {r["Id"]: r for r in data_records}
        return {
            "execution_result": f"Query successful. See {file_name} for the results.",
            "csv_paths": [out_path],
            "records": records_dict,
            "records_df": df,
        }

    def _quickbooks_update_item_with_existing(
        self, entity_type, source, entity_key, payload, existing_entity, chat_log
    ):
        if entity_type == "Purchase":
            return self._quickbooks_purchase_update_with_existing(
                source, entity_key, payload, existing_entity, chat_log
            )
        elif entity_type == "Bill":
            return self._quickbooks_bill_update_with_existing(source, entity_key, payload, existing_entity, chat_log)
        else:
            return {
                "is_update_needed": False,
                "success": False,
                "status": f"Update not supported for {entity_type}",
                "message": f"Update not supported for {entity_type}",
                "createdId": "",
            }

    def _quickbooks_create_item(self, entity_type, payload, chat_log):
        if entity_type == "Purchase":
            return quickbooks_create_purchase(payload)
        elif entity_type == "Bill":
            # TODO: Before creating bill, check for the existing purchases that correspond to the bill and if so convert those to paid bills
            return quickbooks_create_bill(payload)
        else:
            return {
                "success": False,
                "message": f"Create not supported for {entity_type}",
            }

    def _quickbooks_purchase_update_with_existing(
        self, input_source, purchase_key, payload, existing_purchase, chat_log
    ):
        res = {
            "is_update_needed": False,
            "success": False,
            "status": "Expense already exists",
            "message": "Unknown error",
            "createdId": "",
        }
        try:
            if input_source == "statement":
                # If input source is statement and existing purchase is from receipt, then update the payment account id if different
                if "source=receipt" in existing_purchase.get("PrivateNote", ""):
                    if existing_purchase["AccountRef"]["value"] != payload["AccountRef"]["value"]:
                        chat_log.debug(
                            f"Updating payment account for existing purchase {existing_purchase['Id']} for purchase {purchase_key}: {existing_purchase['AccountRef']['value']} -> {payload['AccountRef']['value']}"
                        )
                        existing_lines = existing_purchase["Line"]
                        # If the existing purchase total is different to total amount of the statement record (due to currency difference), then update the line items as well
                        if existing_purchase["TotalAmt"] != payload["TotalAmt"]:
                            convert_ratio = payload["TotalAmt"] / existing_purchase["TotalAmt"]
                            chat_log.debug(
                                f"Updating line items for existing purchase {existing_purchase['Id']} for purchase {purchase_key}: {existing_purchase['TotalAmt']} -> {payload['TotalAmt']} (Ratio: {convert_ratio})"
                            )
                            for line in existing_lines:
                                line["Amount"] = line["Amount"] * convert_ratio

                        update_payload = {
                            "Id": existing_purchase["Id"],
                            "sparse": True,
                            "PaymentType": payload["PaymentType"],
                            "AccountRef": payload["AccountRef"],
                            "SyncToken": existing_purchase["SyncToken"],
                            "Line": existing_lines,
                        }
                        # --- Update Purchase API call ---
                        try:
                            res = quickbooks_update_purchase(update_payload)
                            res["is_update_needed"] = True

                        except Exception as e:
                            res["message"] = str(e)
                        if res.get("success"):
                            res["status"] = "Updated the expense added via receipt"
                            chat_log.debug(
                                f"Updated existing purchase {existing_purchase['Id']} for purchase {purchase_key}"
                            )
                        else:
                            chat_log.error(
                                f"Failed to update existing purchase {existing_purchase['Id']} for purchase {purchase_key}: {res.get('message')}"
                            )
                    else:
                        # Nothing to update
                        chat_log.debug(
                            f"No change needed for existing purchase {existing_purchase['Id']} for purchase {purchase_key} already added from receipt."
                        )
                        res["success"] = True
                else:
                    # This is a duplicate statement input - skip
                    chat_log.debug(f"Duplicate statement input found for purchase {purchase_key}. Skipping...")
                    res["success"] = True
            else:
                # If input from receipt and existing purchase is from statement - update line items, transaction date from the receipt
                if "source=statement" in existing_purchase.get("PrivateNote", ""):
                    # Line item amounts recalculate based on total amount given in statement record (Due to currency difference) if existing purchase total is different to total amount of the statement record
                    if existing_purchase["TotalAmt"] != payload["TotalAmt"]:
                        convert_ratio = existing_purchase["TotalAmt"] / payload["TotalAmt"]
                        chat_log.debug(
                            f"Updating line items for existing purchase {existing_purchase['Id']} for purchase {purchase_key}: {payload['TotalAmt']} -> {existing_purchase['TotalAmt']} (Ratio: {convert_ratio})"
                        )
                        # Correct the TotalAmt in the payload as well
                        payload["TotalAmt"] = existing_purchase["TotalAmt"]
                        for line in payload["Line"]:
                            # Always use original amount for conversion because "Amount" field is already converted to company currency in previous steps
                            if "original_amount" not in line:
                                continue
                            line["Amount"] = line["original_amount"] * convert_ratio

                    # Make sure original_amount is not sent to QuickBooks API
                    for line in payload["Line"]:
                        del line["original_amount"]

                    update_payload = {
                        "Id": existing_purchase["Id"],
                        "sparse": True,
                        "SyncToken": existing_purchase["SyncToken"],
                        "PaymentType": existing_purchase["PaymentType"],
                        "AccountRef": existing_purchase["AccountRef"],
                        "Line": payload["Line"],
                        "TxnDate": payload["TxnDate"],
                        "GlobalTaxCalculation": payload["GlobalTaxCalculation"],
                        "EntityRef": payload["EntityRef"],
                    }
                    # --- Update Purchase API call ---
                    try:
                        res = quickbooks_update_purchase(update_payload)
                        res["is_update_needed"] = True

                    except Exception as e:
                        res["message"] = str(e)
                    if res.get("success"):
                        res["status"] = "Updated the expense added via statement"
                        chat_log.debug(
                            f"Updated existing purchase {existing_purchase['Id']} for purchase {purchase_key}"
                        )
                    else:
                        chat_log.error(
                            f"Failed to update existing purchase {existing_purchase['Id']} for purchase {purchase_key}: {res.get('message')}"
                        )
                else:
                    # Duplicate receipt input - skip
                    chat_log.debug(f"Duplicate receipt input found for purchase {purchase_key}. Skipping...")
                    res["success"] = True
        except Exception as e:
            res["message"] = str(e)
            chat_log.error(f"Error updating existing purchase: {e}: \n{traceback.format_exc()}")

        return res

    def _quickbooks_bill_update_with_existing(self, input_source, bill_key, payload, existing_bill, chat_log):
        # Nothing to do here because always bills are added from invoices.
        chat_log.debug(f"Duplicate bill input found for bill {bill_key}. Skipping...")
        res = {
            "is_update_needed": False,
            "success": True,
            "status": "Bill already exists",
            "message": "Bill already exists",
            "createdId": existing_bill["Id"],
        }
        return res

    def _quickbooks_create_entity_payload(self, source, entity_row, lines_df, chat_id, chat_log, entity_type):
        if entity_type == "Purchase":
            return self._quickbooks_create_purchase_object(source, entity_row, lines_df, chat_id, chat_log)
        elif entity_type == "Bill":
            return self._quickbooks_create_bill_object(source, entity_row, lines_df, chat_id, chat_log)
        else:
            return {
                "is_success": False,
                "error_message": f"Unsupported entity type: {entity_type}",
                "payload": {},
            }

    def _quickbooks_create_purchase_object(self, source, purchase_row, lines_df, chat_id, chat_log):
        purchase_key = purchase_row.get("purchase_key")
        status = "Failed"
        created_id = None
        err = ""
        attach_attempted = 0
        attach_success = 0
        attach_error = 0

        # --- Validate header fields ---
        payment_type = purchase_row.get("payment_type")
        account_id = purchase_row.get("payment_account_id")
        transaction_date = purchase_row.get("transaction_date")
        total_amount = purchase_row.get("total_amount")
        converted_total_amount = total_amount
        currency_code = purchase_row.get("currency", "")
        global_tax_calculation = purchase_row.get("global_tax_calculation", "")
        payee_keyword = purchase_row.get("payee_name", "")
        payee_address = purchase_row.get("payee_address", "")

        # Convert the total amount based on currency code in case of receipts
        if source == "receipt":
            if currency_code and self.company_currency:
                conversion_res = convert_currency(total_amount, currency_code, self.company_currency, chat_log)
                if conversion_res.get("is_success"):
                    converted_total_amount = conversion_res.get("converted_amount")

        payload = {}
        # Find the payee id - either by matching with existing vendor/customer/employee or creating a new vendor
        payee_id, payee_name = self.find_payee(payee_keyword, payee_address, chat_id, chat_log, source)
        # Generate key for doc number
        doc_number = self.generate_reference_key(
            payee_name,
            transaction_date,
            purchase_row.get("timestamp", ""),
            purchase_row.get("invoice_number", ""),
            chat_log,
        )
        if not purchase_key:
            err = "Missing purchase_key"
        elif not payment_type:
            err = "Missing payment_type"
        elif not account_id:
            err = "Missing payment_account_id"
        else:
            # --- Collect and validate lines ---
            lines = lines_df[lines_df["purchase_key"] == purchase_key]
            if lines.empty:
                err = f"No line items for purchase_key {purchase_key}"
            else:
                line_objs = []
                for _, lrow in lines.iterrows():
                    detail_type = lrow.get("detail_type", "AccountBasedExpenseLineDetail")
                    line_amount = lrow.get("line_amount")
                    description = lrow.get("description", "")
                    expense_account_id = lrow.get("expense_account_id")
                    tax_code_id = lrow.get("tax_code_id", None)

                    # Validate line fields
                    if line_amount in [None, ""]:
                        err = f"Missing line_amount on line {lrow.get('line_number')}"
                        break
                    if not expense_account_id:
                        err = f"Missing expense_account_id on line {lrow.get('line_number')}"
                        break
                    try:
                        line_amount_val = float(line_amount)
                        line_amount_original = line_amount_val  # Before currency conversion
                        # convert to the company currency in case of receipts
                        if source == "receipt":
                            if not currency_code or not self.company_currency:
                                chat_log.warning(
                                    f"Missing currency_code or company currency for purchase {purchase_key}"
                                )
                            else:
                                conversion_res = convert_currency(
                                    line_amount_val, currency_code, self.company_currency, chat_log
                                )
                                if conversion_res.get("is_success"):
                                    line_amount_val = conversion_res.get("converted_amount")
                                else:
                                    chat_log.warning(
                                        f"Failed to convert currency for line {lrow.get('line_number')}: {conversion_res.get('error_message')}"
                                    )
                    except:
                        err = f"Invalid line_amount on line {lrow.get('line_number')}"
                        break
                    line_obj = {
                        "DetailType": detail_type,
                        "Amount": line_amount_val,
                        "original_amount": line_amount_original,  # This should be excluded from the payload that use for adding or updating calls
                        "AccountBasedExpenseLineDetail": {"AccountRef": {"value": str(expense_account_id)}},
                        "LineNum": (int(lrow.get("line_number")) if lrow.get("line_number") is not None else None),
                    }
                    if description:
                        line_obj["Description"] = description
                    if tax_code_id:
                        line_obj["AccountBasedExpenseLineDetail"]["TaxCodeRef"] = {"value": str(tax_code_id)}
                    line_objs.append(line_obj)
                if not err:
                    # --- Build header payload ---
                    payload = {
                        "PaymentType": payment_type,
                        "AccountRef": {"value": str(account_id)},
                        "TotalAmt": total_amount,
                        "converted_total_amount": converted_total_amount,
                        "Line": line_objs,
                        "PrivateNote": "source=" + source,
                    }
                    # Optional fields
                    if transaction_date not in [None, "", pd.NaT]:
                        payload["TxnDate"] = str(transaction_date)
                    if purchase_row.get("payment_method_id") not in [None, ""]:
                        payload["PaymentMethodRef"] = {"value": str(purchase_row.get("payment_method_id"))}
                    if global_tax_calculation not in [None, ""]:
                        payload["GlobalTaxCalculation"] = global_tax_calculation
                    if payee_id:
                        payload["EntityRef"] = {"value": str(payee_id)}
                    if doc_number:
                        payload["DocNumber"] = doc_number

        chat_log.debug(f"Payload for purchase {purchase_key}: {payload}")
        return {
            "is_success": not err,
            "payload": payload,
            "error_message": "Problem processing the purchase with key " + str(purchase_key) + ": " + str(err),
        }

    def _quickbooks_create_bill_object(self, source, bill_row, lines_df, chat_id, chat_log):
        bill_key = bill_row.get("bill_key")
        status = "Failed"

        # --- Validate header fields ---
        vendor_name = bill_row.get("vendor_name")
        vendor_address = bill_row.get("vendor_address", "")
        ap_account_id = bill_row.get("ap_account_id")
        transaction_date = bill_row.get("transaction_date")
        due_date = bill_row.get("due_date", "")
        payment_term = bill_row.get("payment_term", "")
        total_amount = bill_row.get("total_amount")
        converted_total_amount = total_amount
        currency_code = bill_row.get("currency", "")
        global_tax_calculation = bill_row.get("global_tax_calculation", "")
        # Convert the total amount based on currency code in case of invoices
        if source == "invoice":
            if currency_code and self.company_currency:
                conversion_res = convert_currency(total_amount, currency_code, self.company_currency, chat_log)
                if conversion_res.get("is_success"):
                    converted_total_amount = conversion_res.get("converted_amount")

        payload = {}
        vendor_id, vendor_name = self.find_payee(vendor_name, vendor_address, chat_id, chat_log, source, ["Vendor"])
        if not vendor_id:
            err = f"Failed to find vendor for bill_key {bill_key}"
            return {
                "is_success": False,
                "payload": payload,
                "error_message": err,
            }
        # Generate key for doc number
        doc_number = self.generate_reference_key(
            vendor_name, transaction_date, bill_row.get("timestamp", ""), bill_row.get("invoice_number", ""), chat_log
        )

        # --- Collect and validate lines ---
        lines = lines_df[lines_df["bill_key"] == bill_key]
        if lines.empty:
            err = f"No line items for bill_key {bill_key}"
        else:
            line_objs = []
            for _, lrow in lines.iterrows():
                detail_type = lrow.get("detail_type", "AccountBasedExpenseLineDetail")
                line_amount = lrow.get("line_amount")
                description = lrow.get("description", "")
                expense_account_id = lrow.get("expense_account_id")
                tax_code_id = lrow.get("tax_code_id", None)

                # Validate line fields
                if not line_amount:
                    err = f"Missing line_amount on line {lrow.get('line_number')}"
                    break
                if not expense_account_id:
                    err = f"Missing expense_account_id on line {lrow.get('line_number')}"
                    break
                # Convert line amount to the company currency
                try:
                    line_amount_val = float(line_amount)
                    line_amount_original = line_amount_val  # Before currency conversion
                    if currency_code and self.company_currency and currency_code != self.company_currency:
                        conversion_res = convert_currency(
                            line_amount_val, currency_code, self.company_currency, chat_log
                        )
                        if conversion_res.get("is_success"):
                            line_amount_val = conversion_res.get("converted_amount")
                        else:
                            chat_log.warning(
                                f"Failed to convert currency for line {lrow.get('line_number')}: {conversion_res.get('error_message')}"
                            )
                except:
                    err = f"Invalid line_amount on line {lrow.get('line_number')}"
                    break
                line_obj = {
                    "DetailType": detail_type,
                    "Amount": line_amount_val,
                    "original_amount": line_amount_original,  # This should be excluded from the payload that use for adding or updating calls
                    "AccountBasedExpenseLineDetail": {"AccountRef": {"value": str(expense_account_id)}},
                    "LineNum": (int(lrow.get("line_number")) if lrow.get("line_number") is not None else None),
                }
                if description:
                    line_obj["Description"] = description
                if tax_code_id:
                    line_obj["AccountBasedExpenseLineDetail"]["TaxCodeRef"] = {"value": str(tax_code_id)}
                line_objs.append(line_obj)
            if not err:
                # --- Build header payload ---
                payload = {
                    "VendorRef": {"value": str(vendor_id)},
                    "TotalAmt": total_amount,
                    "Line": line_objs,
                    "PrivateNote": "source=" + source,
                    "converted_total_amount": converted_total_amount,
                }
                # Optional fields
                if ap_account_id:
                    payload["APAccountRef"] = {"value": str(ap_account_id)}
                if transaction_date not in [None, "", pd.NaT]:
                    payload["TxnDate"] = str(transaction_date)
                if due_date not in [None, "", pd.NaT]:
                    payload["DueDate"] = str(due_date)
                if payment_term not in [None, "", pd.NaT]:
                    payload["PaymentTermRef"] = {"value": str(payment_term)}
                if global_tax_calculation not in [None, ""]:
                    payload["GlobalTaxCalculation"] = global_tax_calculation
                if doc_number:
                    payload["DocNumber"] = doc_number

        chat_log.debug(f"Payload for bill {bill_key}: {payload}")
        return {
            "is_success": not err,
            "payload": payload,
            "error_message": "Problem processing the bill with key " + str(bill_key) + ": " + str(err),
        }

    def _find_matched_existing_item(
        self,
        entity_type: str,
        item_data_object: dict,
        is_from_statement: bool,
        exclude_item_ids: list,
        chat_id,
        chat_log,
    ):
        try:
            # Get transaction date and ensure it's a datetime object
            transaction_date_str = item_data_object.get("TxnDate")
            if not transaction_date_str:
                chat_log.error("No transaction_date found in item_data_object")
                return None

            # Convert string to datetime if needed
            if isinstance(transaction_date_str, str):
                try:
                    transaction_date = datetime.strptime(transaction_date_str, "%Y-%m-%d").date()
                except ValueError:
                    try:
                        transaction_date = datetime.strptime(transaction_date_str, "%Y-%m-%d %H:%M:%S").date()
                    except ValueError:
                        try:
                            transaction_date = datetime.strptime(transaction_date_str, "%Y-%m-%dT%H:%M:%SZ").date()
                        except ValueError:
                            chat_log.error(f"Unable to parse transaction_date: {transaction_date_str}")
                            return None
            else:
                transaction_date = transaction_date_str

            # Calculate date range
            # If the item data is from statement, then check with records starting from 5 days before the transaction date to the transaction date
            # If the item data is from receipt, then check with records starting from the transaction date to +5 days from the transaction date
            from_date = transaction_date - timedelta(days=5) if is_from_statement else transaction_date
            to_date = (
                transaction_date + timedelta(days=5) if is_from_statement else transaction_date + timedelta(days=5)
            )

            # Convert dates to strings for SQL query
            from_date_str = from_date.strftime("%Y-%m-%d")
            to_date_str = to_date.strftime("%Y-%m-%d")
            # Exclude the already processed items - to prevent finding from the same batch
            exclude_id_list_quoted = ",".join([f"'{id}'" for id in exclude_item_ids])

            """
            Enhanced logic with simplified matching
            For all existing transactions in the same source,
                - Just need to check if its duplicated, no need to do a cognitive search
                - Match by vendor, transaction date (same date filter) and total amount (exact match)
                - If still found multiple, then check the reference key (derived from invoice number / timestamp at extraction)
            For existing transactions from other sources
                - Match by transaction date (date range filter), total amount (within 5%)
                - If still found multiple, then do a cognitive search to compare vendor and line items

            """
            # +/-5% total amount range (to account for currency conversion)
            total_amount_min = item_data_object.get("converted_total_amount") * 0.95
            total_amount_max = item_data_object.get("converted_total_amount") * 1.05
            # Query items for same source matching
            item_query_same_source = f"WHERE TxnDate = '{transaction_date_str}' AND TotalAmt < '{total_amount_max}' AND TotalAmt > '{total_amount_min}'"
            same_source_filter = "source=statement" if is_from_statement else "source=receipt"
            if exclude_item_ids:
                item_query_same_source += f" AND Id NOT IN ({exclude_id_list_quoted})"
            # Add vendor filter for purchases and bills
            vendor_id = ""
            if entity_type == "Purchase":
                # item_query_same_source += f" AND EntityRef = 'Vendor|{item_data_object.get('EntityRef').get('value')}'"
                vendor_id = item_data_object.get("EntityRef").get("value")
            elif entity_type == "Bill":
                vendor_id = item_data_object.get("VendorRef").get("value")
                # item_query_same_source += f" AND VendorRef.value = '{item_data_object.get('VendorRef').get('value')}'"
            chat_log.info(f"Query for same source matching: {item_query_same_source}")

            res_same_source = quickbooks_query_object(entity_type, item_query_same_source)
            if not res_same_source.get("success"):
                chat_log.warning(f"Failed to query same source matching: {res_same_source.get('message')}")
            elif res_same_source.get("data"):
                object_type_records = res_same_source["data"].get("QueryResponse", {})
                if object_type_records and entity_type in object_type_records:
                    # Filter by vendor id and private note (source)
                    potential_match_items_same_source = [
                        item
                        for item in object_type_records[entity_type]
                        if item.get("EntityRef").get("value") == vendor_id
                        and same_source_filter in item.get("PrivateNote", "")
                    ]
                    # If still comes multiple from same source, then check reference key
                    if not potential_match_items_same_source:
                        chat_log.debug(f"No potential match {entity_type.lower()} items in same source found")
                    elif len(potential_match_items_same_source) == 1:
                        chat_log.debug(f"Found 1 potential match {entity_type.lower()} item in same source")
                        return potential_match_items_same_source[0]
                    else:
                        # Check reference key (DocNumber)
                        reference_key_input = item_data_object.get("DocNumber", "")
                        if reference_key_input:
                            for item in potential_match_items_same_source:
                                if item.get("DocNumber", "") == reference_key_input:
                                    chat_log.debug(
                                        f"Found match {entity_type.lower()} item in same source by reference key {reference_key_input}"
                                    )
                                    return item
                        # If still not found, then wait for different source matching
            else:
                chat_log.debug(f"No potential match {entity_type.lower()} items in same source found")

            # Query items for different source matching
            potential_match_items_different_source = []
            item_query_different_source = f"WHERE TxnDate >= '{from_date_str}' AND TxnDate <= '{to_date_str}' AND \
                TotalAmt > '{total_amount_min}' AND TotalAmt < '{total_amount_max}'"
            if exclude_item_ids:
                item_query_different_source += f" AND Id NOT IN ({exclude_id_list_quoted})"
            chat_log.info(f"Query for different source matching: {item_query_different_source}")

            res_different_source = quickbooks_query_object(entity_type, item_query_different_source)
            if not res_different_source.get("success"):
                chat_log.warning(f"Failed to query different source matching: {res_different_source.get('message')}")
                return None
            elif res_different_source.get("data"):
                object_type_records = res_different_source["data"].get("QueryResponse", {})
                if object_type_records and entity_type in object_type_records:
                    items_in_range = object_type_records[entity_type]
                    # Now filter those by source (source should be receipt or invoice if is_from_statement, else it should be statement)
                    potential_match_items_different_source = [
                        item
                        for item in items_in_range
                        if (
                            (is_from_statement and "source=statement" not in item.get("PrivateNote", ""))
                            or (not is_from_statement and "source=statement" in item.get("PrivateNote", ""))
                        )
                    ]

            if not potential_match_items_different_source:
                chat_log.debug(f"No potential match {entity_type.lower()} items in different source found")
                return None
            else:
                # Do a cognitive search to compare vendor and line items
                items_df = pd.DataFrame(potential_match_items_different_source)
                matching_guidelines = f"1. Vendor: Check both vendor names to see whether both of them are referring to the same company.\n\
                    2. Line item descriptions: Determine whether the line item descriptions are referring to the same products/services.\n\
                    3. Transaction date: In case of multiple matches, choose the one which has the transaction date closest to the given transaction date.\n\
                    4. Total amount: In case of multiple matches, choose the one which has the total amount closest to the amount in the given {entity_type.lower()}."
                search_query = f"Find the {entity_type.lower()} transaction that matches the given {entity_type.lower()}. \nGuidelines for matching: \n{matching_guidelines}"
                matched_df = intelligent_text_match(
                    chat_id, items_df, ["TotalAmt", "TxnDate", "Line", "PrivateNote"], item_data_object, search_query
                )
                if not matched_df.empty:
                    chat_log.debug(
                        f"Found {len(matched_df)} matches for {entity_type.lower()} from the list of {len(potential_match_items_different_source)} potential matches"
                    )
                    match_records_list = matched_df.to_dict("records")
                    chat_log.debug(f"All matches:\n {json.dumps(match_records_list, indent=4)}\n")
                    # Return the first match as a dictionary
                    return matched_df.iloc[0].to_dict()
                else:
                    chat_log.debug(
                        f"No match found for {entity_type.lower()} from the list of {len(potential_match_items_different_source)} potential matches"
                    )
                    return None

            # # Filter out the items that amounts are different (more than 5%)
            # total_line_amount = 0
            # for line in item_data_object.get("Line"):
            #     total_line_amount += line.get("Amount")
            # potential_match_items = []
            # for item in items_in_range:
            #     if not item.get("TotalAmt") or not total_line_amount:
            #         continue  # 0 amounts are not valid
            #     if (
            #         abs(item.get("TotalAmt") - total_line_amount) / item_data_object.get("TotalAmt")
            #         < 0.05
            #     ):
            #         potential_match_items.append(item)
            # if not potential_match_items:
            #     chat_log.debug(
            #         f"No potential match found for {entity_type.lower()} item {item_data_object} from the list of {len(items_in_range)} items in the date range {from_date_str} to {to_date_str}"
            #     )
            #     return None  # No potential match found
            # Set PrivateNote for items that don't have it
            # for item in potential_match_items:
            #     if not item.get("PrivateNote"):
            #         item["PrivateNote"] = ""
            # chat_log.debug(
            #     f"Found {len(potential_match_items)} potential matches for {entity_type.lower()} item {item_data_object} from the list of {len(items_in_range)} items in the date range {from_date_str} to {to_date_str}, excluding items {exclude_item_ids}"
            # )
            # # Convert the items array to dataframe
            # items_df = pd.DataFrame(potential_match_items)
            # # Use cognitive search to find the most similar item
            # matching_guidelines = f"1 - TxnDate: should be between {from_date_str} and {to_date_str} if the transaction source in PrivateNote is different, else the TxnDate should be the same.\n\
            #     2 - TotalAmt: should be within 5% of the amount in the {entity_type.lower()}.\n\
            #     3 - Line item description: Should intelligently compare by vendor and product/service name based on available information."
            # search_query = f"Find the {entity_type.lower()} transaction that matches this:\n {json.dumps(item_data_object)}\n{matching_guidelines}"
            # matched_df = intelligent_text_match(
            #     chat_id, items_df, ["TotalAmt", "TxnDate", "Line", "PrivateNote"], search_query
            # )
            # if matched_df.empty:
            #     chat_log.debug(
            #         f"No match found for {entity_type.lower()} from the list of {len(potential_match_items)} potential matches"
            #     )
            #     return None  # No match found
            # Report the no. of matches found
            # chat_log.debug(
            #     f"Found {len(matched_df)} matches for {entity_type.lower()} from the list of {len(potential_match_items)} potential matches"
            # )
            # match_records_list = matched_df.to_dict("records")
            # chat_log.debug(f"All matches:\n {json.dumps(match_records_list, indent=4)}\n")
            # # Return the first match as a dictionary
            # return matched_df.iloc[0].to_dict()
        except Exception as e:
            chat_log.error(f"Error finding existing {entity_type.lower()}: {e}: \n{traceback.format_exc()}")
            return None

    """
    Description: Update existing expenses in QuickBooks
    Input: CSV file with following columns:
    1. purchase_id
    2. payment_account_id (optional)
    3. transaction_date (optional)
    4. total_amount (optional)
    5. payment_method_id (optional)
    6. global_tax_calculation (optional)
    Output: List of csv files with reported status of each expense recording
    """

    def update_expenses(
        self, conversation: Conversation, source: str, data_file_path_list: list, task_instruction, chat_log
    ):
        # TODO: Update vendor
        # First validate whether purchase_updates.csv and purchase_line_updates.csv is present in the data_file_path_list
        purchases_csv_path = None
        purchase_lines_csv_path = None
        for data_file_path in data_file_path_list:
            if data_file_path.endswith("purchase_updates.csv"):
                purchases_csv_path = data_file_path
            elif data_file_path.endswith("purchase_line_updates.csv"):
                purchase_lines_csv_path = data_file_path

        if not purchases_csv_path:
            return {
                "execution_result": "Failed to find purchase_updates.csv file. Please check the file names and try again.",
                "csv_paths": [],
            }

        # If line updates file not given, then create it with single line for each purchase in purchase_updates.csv
        if not purchase_lines_csv_path:
            chat_log.debug("No purchase_line_updates.csv file found. Creating one")
            try:
                purchases_df = load_data(conversation.chat_id, "purchase_updates.csv")
                purchase_lines = []
                for _, row in purchases_df.iterrows():
                    purchase_lines.append(
                        {
                            "purchase_id": row["purchase_id"],
                            "line_number": 1,
                            "detail_type": "AccountBasedExpenseLineDetail",
                            "line_amount": row.get("total_amount", ""),
                            "description": row.get("payee_name", ""),
                        }
                    )
                save_data(conversation.chat_id, "purchase_line_updates.csv", purchase_lines)
            except Exception as e:
                chat_log.error(f"Error creating purchase_line_updates.csv: {e}")
                return {
                    "execution_result": f"Error creating purchase_line_updates.csv: {e}",
                    "csv_paths": [],
                }

        chat_id = conversation.chat_id
        # --- Load Data ---
        try:
            purchases_df = load_data(chat_id, "purchase_updates.csv")
            lines_df = load_data(chat_id, "purchase_line_updates.csv")
        except Exception as e:
            chat_log.error(f"Error loading purchase_updates.csv: {e}")
            return {
                "execution_result": f"Error loading purchase_updates.csv: {e}",
                "csv_paths": [],
            }
        # --- Validate mandatory fields (purchase_id) in purchase_updates.csv ---
        if "purchase_id" not in purchases_df.columns:
            return {
                "execution_result": "Missing mandatory field (purchase_id) in purchase_updates.csv",
                "csv_paths": [],
            }
        # --- Validate mandatory fields (purchase_id, line_number) in purchase_line_updates.csv ---
        if "purchase_id" not in lines_df.columns or "line_number" not in lines_df.columns:
            return {
                "execution_result": "Missing mandatory fields (purchase_id, line_number) in purchase_line_updates.csv",
                "csv_paths": [],
            }

        # We can only update payment account id, transaction date, total amount, payment method id, global tax calculation
        if (
            "transaction_date" not in purchases_df.columns
            and "total_amount" not in purchases_df.columns
            and "payment_method_id" not in purchases_df.columns
            and "global_tax_calculation" not in purchases_df.columns
            and "line_amount" not in lines_df.columns
        ):
            return {
                "execution_result": "No valid fields to update in purchase_updates.csv",
                "csv_paths": [],
            }
        # Query existing transactions to get the existing field values
        existing_purchases = self.query_transactions(
            conversation, "Purchase", chat_log, id_list=purchases_df["purchase_id"].tolist()
        )

        results = []
        updated_ids = []
        # Track the first updated at time - in local timezone - in string format: YYYY-MM-DDTHH:MM:SS-HH:MM
        company_tz = ZoneInfo("America/Winnipeg")
        local_time = datetime.now().astimezone(company_tz)
        # Format in ISO 8601 with offset
        first_updated_cutoff = local_time.isoformat()
        csv_output_file_paths = []

        # --- Process Updates ---
        for _, row in purchases_df.iterrows():
            purchase_id = str(row.get("purchase_id"))
            # Load matching existing purchase
            existing_purchase = existing_purchases["records"].get(str(purchase_id))
            if not existing_purchase:
                chat_log.warning(f"Existing purchase not found for purchase_id {purchase_id}")
                continue
            # --- Build update payload with mandatory fields from existing purchase ---
            payload = {
                "Id": purchase_id,
                "SyncToken": existing_purchase["SyncToken"],
                "sparse": True,
                "Line": existing_purchase["Line"],
                "PrivateNote": "source=" + source,
                "PaymentType": existing_purchase["PaymentType"],
                "AccountRef": existing_purchase["AccountRef"],
                "GlobalTaxCalculation": existing_purchase["GlobalTaxCalculation"],
            }
            if "payment_account_id" in purchases_df.columns:
                payload["AccountRef"] = {"value": row.get("payment_account_id")}
            if "transaction_date" in purchases_df.columns:
                payload["TxnDate"] = str(row.get("transaction_date"))
            if "payment_method_id" in purchases_df.columns:
                payload["PaymentMethodRef"] = {"value": row.get("payment_method_id")}
            if "global_tax_calculation" in purchases_df.columns:
                payload["GlobalTaxCalculation"] = row.get("global_tax_calculation")

            # Fill the line items
            for _, line_row in lines_df.iterrows():
                if line_row.get("purchase_id") == purchase_id:
                    line_obj = {
                        "Id": line_row.get("line_number"),
                        "Amount": float(line_row.get("line_amount")),
                    }
                    if line_row.get("description") not in [None, ""]:
                        line_obj["Description"] = line_row.get("description")
                    if line_row.get("detail_type") not in [None, ""]:
                        line_obj["DetailType"] = line_row.get("detail_type")
                    if line_row.get("expense_account_id") not in [None, ""]:
                        line_obj["AccountBasedExpenseLineDetail"] = {
                            "AccountRef": {"value": line_row.get("expense_account_id")}
                        }
                    if line_row.get("tax_code_id") not in [None, ""]:
                        line_obj["TaxCodeRef"] = {"value": line_row.get("tax_code_id")}
                    payload["Line"].append(line_obj)

            # --- Update Purchase API call ---
            try:
                chat_log.debug(f"Payload for purchase update {purchase_id}:\n {payload}")
                res = self._quickbooks_update_item_with_existing(
                    "Purchase", source, purchase_id, payload, existing_purchase, chat_log
                )  # quickbooks_update_purchase(payload)
            except Exception as e:
                chat_log.error(
                    f"Error updating purchase {purchase_id}: {e}, payload: {payload}\n{traceback.format_exc()}"
                )
                results.append(
                    {"purchase_id": purchase_id, "success": False, "status": "Update failed", "error": str(e)}
                )
                continue
            if not res.get("success"):
                results.append(
                    {
                        "purchase_id": purchase_id,
                        "success": False,
                        "status": "Update failed",
                        "error": res.get("message", "Unknown error"),
                    }
                )
                continue

            results.append({"purchase_id": purchase_id, "success": True, "status": "Update successful", "error": ""})

            updated_ids.append(purchase_id)

        results_df = pd.DataFrame(results)
        # Do a verification call to QBO API to check whether the purchase records are successfully updated
        updated_entries_file_name, n_verified = self.review_updated_entries(
            chat_id, "Purchase", first_updated_cutoff, updated_ids, chat_log
        )
        if updated_entries_file_name:
            conversation.data_reference_set.add(updated_entries_file_name)
            csv_output_file_paths.append(f"storage/public/{chat_id}/files/{updated_entries_file_name}")

        # Provide summary
        n_success = sum(r["success"] == True for r in results)
        n_failed = sum(r["success"] == False for r in results)
        chat_log.info(
            f"{n_success} success, {n_failed} failed, {n_verified} verified line items, result summary: \n{results_df.to_markdown(index=False)}"
        )
        return {
            "execution_result": (
                f"{n_success} success, {n_failed} failed, expense update summary: \n{results_df.to_markdown(index=False)} \n\n{n_verified} Verification of updated line items in QBO — see review_updated_expenses.csv"
                if updated_entries_file_name
                else "Verification of updated entries failed."
            ),
            "csv_paths": csv_output_file_paths,
        }

    """
    Description: Find the payee id for a purchase - either by matching with existing vendor/customer/employee or creating a new vendor
    Input: payee_keyword: Keyword from the receipt to search for the payee
    Output: payee_id, payee_name : Id of the vendor/customer/employee found or created + name of the payee
    """

    def find_payee(
        self,
        payee_keyword: str,
        payee_address: str,
        chat_id: str,
        chat_log: Logger,
        input_source="receipt",
        check_entity_list=["Vendor", "Customer", "Employee"],
    ):
        if not payee_keyword:
            return None
        payee_record = None
        payee_id = None
        payee_name = None
        if "Vendor" in check_entity_list:
            # First query from available vendors which has starting letter
            query = f"WHERE Active = true AND DisplayName LIKE '%{payee_keyword.lower()[0]}%'"
            res = quickbooks_query_object("Vendor", query)
            if res.get("success") and res.get("data"):
                object_type_records = res["data"].get("QueryResponse", {})
                if object_type_records and "Vendor" in object_type_records:
                    chat_log.debug(
                        f"Found {len(object_type_records['Vendor'])} vendors for matching to {payee_keyword}"
                    )
                    # Cognitive match to determine the best matching vendor, return null if no match
                    search_query = f"Find the vendor that matches this name: {payee_keyword}."
                    matching_guidelines = f"\n\nGuidelines for matching:\n1. There can be different names that refers to the same company.\n\
                        2. If non of the given names relate to the given name, then return none."
                    vendors_df = pd.DataFrame(object_type_records["Vendor"])
                    matched_df = intelligent_text_match(
                        chat_id, vendors_df, ["DisplayName"], {}, search_query + matching_guidelines
                    )
                    if not matched_df.empty:
                        payee_record = vendors_df.iloc[matched_df.index[0]].to_dict()
                        chat_log.debug(f"Found {len(matched_df)} matching vendors for {payee_keyword}")
                    else:
                        chat_log.debug(f"Cognitive search did not find any matching vendor for {payee_keyword}")
            if payee_record:
                payee_id = payee_record.get("Id")
                payee_name = payee_record.get("DisplayName")
                chat_log.info(f"Found vendor {payee_id} for {payee_keyword}")
                # If this is from a receipt/invoice, then update the vendor details if different
                if input_source in ["receipt", "invoice"]:
                    self._check_and_update_vendor_details(payee_record, payee_keyword, payee_address, chat_log)
                return payee_id, payee_name

        if "Customer" in check_entity_list:
            # Secondly, query for available customers
            query = f"WHERE Active = true AND DisplayName LIKE '%{payee_keyword}%'"
            res = quickbooks_query_object("Customer", query)
            if res.get("success") and res.get("data"):
                object_type_records = res["data"].get("QueryResponse", {})
                if object_type_records and "Customer" in object_type_records:
                    payee_record = object_type_records["Customer"][0]
            if payee_record:
                payee_id = payee_record.get("Id")
                payee_name = payee_record.get("DisplayName")
                chat_log.info(f"Found customer {payee_id} for {payee_keyword}")
                return payee_id, payee_name

        if "Employee" in check_entity_list:
            # Thirdly, query for available employees
            query = f"WHERE Active = true AND DisplayName LIKE '%{payee_keyword}%'"
            res = quickbooks_query_object("Employee", query)
            if res.get("success") and res.get("data"):
                object_type_records = res["data"].get("QueryResponse", {})
                if object_type_records and "Employee" in object_type_records:
                    payee_record = object_type_records["Employee"][0]
            if payee_record:
                payee_id = payee_record.get("Id")
                payee_name = payee_record.get("DisplayName")
                chat_log.info(f"Found employee {payee_id} for {payee_keyword}")
                return payee_id, payee_name

        # If no match found, create a new vendor
        payload = {
            "DisplayName": payee_keyword,
            "PrintOnCheckName": payee_keyword,
            "BillAddr": {"Line1": payee_address},
        }
        res = quickbooks_create_vendor(payload)
        if res.get("success"):
            payee_id = res.get("createdId")
            chat_log.info(f"Created new vendor {payee_id} for {payee_keyword}")
        else:
            chat_log.info(f"Failed to create new vendor for {payee_keyword}")
        return payee_id, payee_keyword

    """
    Description: Retrieve the created entries from QuickBooks and review the status
    Input: 
        entity_type: Type of the entity to review (e.g. Vendor, Bill, Purchase)
        created_at: Time which first of the entries were created in order to query by MetaData.CreateTime
        created_ids: List of ids of the created entries
    Output: CSV file with created entries, no of records created
    """

    def review_created_entries(
        self, chat_id: str, entity_type: str, created_at: str, created_ids: list, chat_log: Logger
    ):
        query = f"WHERE MetaData.CreateTime >= '{created_at}' ORDER BY MetaData.CreateTime ASC"
        res = quickbooks_query_object(entity_type, query)
        if res.get("success") and res.get("data"):
            object_type_records = res["data"].get("QueryResponse", {})
            if object_type_records and entity_type in object_type_records:
                df = self._parse_created_entries(entity_type, object_type_records[entity_type], chat_log)
                df = df[df["Id"].isin(created_ids)]
                if not df.empty:
                    file_name = f"confirmation_of_created_{entity_type.lower()}.csv"
                    out_path = f"storage/public/{chat_id}/files/{file_name}"
                    df.to_csv(out_path, index=False)
                    chat_log.info(f"Review file created at {out_path}")
                    return file_name, len(df)
                else:
                    chat_log.info(f"No created entries found for {entity_type} with ids {created_ids}")
        return None, 0

    def review_updated_entries(
        self, chat_id: str, entity_type: str, updated_at: str, updated_ids: list, chat_log: Logger
    ):
        # Convert id list to quoted string list
        updated_ids_list = [("'" + str(i) + "'") for i in updated_ids]
        query = f"WHERE Id IN ({','.join(updated_ids_list)})"
        res = quickbooks_query_object(entity_type, query)
        if res.get("success") and res.get("data"):
            object_type_records = res["data"].get("QueryResponse", {})
            if object_type_records and entity_type in object_type_records:
                df = self._parse_created_entries(entity_type, object_type_records[entity_type], chat_log)
                if not df.empty:
                    file_name = f"confirmation_of_updated_{entity_type.lower()}.csv"
                    out_path = f"storage/public/{chat_id}/files/{file_name}"
                    df.to_csv(out_path, index=False)
                    chat_log.info(f"Review file created at {out_path}")
                    return file_name, len(df)
                else:
                    chat_log.info(f"No updated entries found for {entity_type} with ids {updated_ids}")
        return None, 0

    """
    Description: Parse the created entries in to user friendly format and return a dataframe
    Input: 
        entity_type: Type of the entity to review (e.g. Vendor, Bill, Purchase)
        records: List of records returned from the query
            eg: [{'AccountRef': {'value': '**********', 'name': 'Rogers Credit Card'}, 'PaymentType': 'CreditCard', 'EntityRef': {'value': '6', 'name': 'Staples Canada', 'type': 'Vendor'}, 'Credit': False, 'TotalAmt': 105.82, 'GlobalTaxCalculation': 'NotApplicable', 'PurchaseEx': {'any': [{'name': '{http://schema.intuit.com/finance/v3}NameValue', 'declaredType': 'com.intuit.schema.finance.v3.NameValue', 'scope': 'javax.xml.bind.JAXBElement$GlobalScope', 'value': {'Name': 'TxnType', 'Value': '54'}, 'nil': False, 'globalScope': True, 'typeSubstituted': False}]}, 'domain': 'QBO', 'sparse': False, 'Id': '41', 'SyncToken': '1', 'MetaData': {'CreateTime': '2025-09-09T11:10:06-07:00', 'LastUpdatedTime': '2025-09-09T11:10:19-07:00'}, 'TxnDate': '2025-09-02', 'CurrencyRef': {'value': 'CAD', 'name': 'Canadian Dollar'}, 'Line': [{'Id': '1', 'Description': 'CANON TR7620A WIRELESS PRINTER', 'Amount': 89.99, 'DetailType': 'AccountBasedExpenseLineDetail', 'AccountBasedExpenseLineDetail': {'AccountRef': {'value': '42', 'name': 'Supplies'}, 'BillableStatus': 'NotBillable', 'TaxCodeRef': {'value': '7'}}, 'CustomExtensions': []}, {'Id': '2', 'Description': 'RECYCLING FEE', 'Amount': 4.5, 'DetailType': 'AccountBasedExpenseLineDetail', 'AccountBasedExpenseLineDetail': {'AccountRef': {'value': '42', 'name': 'Supplies'}, 'BillableStatus': 'NotBillable', 'TaxCodeRef': {'value': '7'}}, 'CustomExtensions': []}], 'TxnTaxDetail': {'TotalTax': 11.33, 'TaxLine': [{'Amount': 4.72, 'DetailType': 'TaxLineDetail', 'TaxLineDetail': {'TaxRateRef': {'value': '3'}, 'PercentBased': True, 'TaxPercent': 5, 'NetAmountTaxable': 94.49}}, {'Amount': 6.61, 'DetailType': 'TaxLineDetail', 'TaxLineDetail': {'TaxRateRef': {'value': '15'}, 'PercentBased': True, 'TaxPercent': 7, 'NetAmountTaxable': 94.49}}]}}, {'AccountRef': {'value': '**********', 'name': 'Rogers Credit Card'}, 'PaymentType': 'CreditCard', 'EntityRef': {'value': '6', 'name': 'Staples Canada', 'type': 'Vendor'}, 'Credit': False, 'TotalAmt': 7.83, 'GlobalTaxCalculation': 'NotApplicable', 'PurchaseEx': {'any': [{'name': '{http://schema.intuit.com/finance/v3}NameValue', 'declaredType': 'com.intuit.schema.finance.v3.NameValue', 'scope': 'javax.xml.bind.JAXBElement$GlobalScope', 'value': {'Name': 'TxnType', 'Value': '54'}, 'nil': False, 'globalScope': True, 'typeSubstituted': False}]}, 'domain': 'QBO', 'sparse': False, 'Id': '42', 'SyncToken': '1', 'MetaData': {'CreateTime': '2025-09-09T11:11:30-07:00', 'LastUpdatedTime': '2025-09-09T11:11:35-07:00'}, 'TxnDate': '2025-09-02', 'CurrencyRef': {'value': 'CAD', 'name': 'Canadian Dollar'}, 'Line': [{'Id': '1', 'Description': 'OB FSC PAPER REAM', 'Amount': 6.99, 'DetailType': 'AccountBasedExpenseLineDetail', 'AccountBasedExpenseLineDetail': {'AccountRef': {'value': '40', 'name': 'Stationery and printing'}, 'BillableStatus': 'NotBillable', 'TaxCodeRef': {'value': '7'}}, 'CustomExtensions': []}], 'TxnTaxDetail': {'TotalTax': 0.84, 'TaxLine': [{'Amount': 0.35, 'DetailType': 'TaxLineDetail', 'TaxLineDetail': {'TaxRateRef': {'value': '3'}, 'PercentBased': True, 'TaxPercent': 5, 'NetAmountTaxable': 6.99}}, {'Amount': 0.49, 'DetailType': 'TaxLineDetail', 'TaxLineDetail': {'TaxRateRef': {'value': '15'}, 'PercentBased': True, 'TaxPercent': 7, 'NetAmountTaxable': 6.99}}]}}]
    Output: Dataframe with parsed records
    """

    def _parse_created_entries(
        self, entity_type: str, records: list, chat_log: Logger, is_combine_line_items: bool = False
    ):
        chat_log.info(f"Checking {len(records)} records for {entity_type}")
        parsed_records = []
        for record in records:
            parsed_record = {}
            if entity_type == "Purchase":
                # Separate records for each line if is_combine_line_items is False, else combine all line item descriptions only by comma
                if is_combine_line_items:
                    line_descriptions = []
                    for line in record.get("Line"):
                        if line.get("Description"):
                            line_descriptions.append(line.get("Description"))
                    line_descriptions = ", ".join(line_descriptions)
                    tax_lines_str = ""
                    if "TxnTaxDetail" in record and "TaxLine" in record["TxnTaxDetail"]:
                        for tax_line in record["TxnTaxDetail"]["TaxLine"]:
                            tax_lines_str += f"{tax_line['Amount']}@{tax_line['TaxLineDetail']['TaxPercent'] if tax_line['TaxLineDetail'].get('TaxPercent') else ''}, "
                    parsed_record = {
                        "Id": record["Id"],
                        "Payee": record["EntityRef"]["name"] if record.get("EntityRef", {}).get("name") else "",
                        "TxnDate": record.get("TxnDate", ""),
                        "TotalAmt": record.get("TotalAmt"),
                        "LineDescription": line_descriptions,
                        "GlobalTaxCalculation": record.get("GlobalTaxCalculation"),
                        "TaxLines": tax_lines_str,
                    }
                else:
                    for line in record.get("Line"):
                        tax_lines_str = ""
                        if "TxnTaxDetail" in record and "TaxLine" in record["TxnTaxDetail"]:
                            for tax_line in record["TxnTaxDetail"]["TaxLine"]:
                                tax_lines_str += f"{tax_line['Amount']}@{tax_line['TaxLineDetail']['TaxPercent'] if tax_line['TaxLineDetail'].get('TaxPercent') else ''}, "
                        parsed_record = {
                            "Id": record["Id"],
                            "Payee": record["EntityRef"]["name"] if record.get("EntityRef", {}).get("name") else "",
                            "TxnDate": record.get("TxnDate", ""),
                            "TotalAmt": record.get("TotalAmt"),
                            "LineDescription": line.get("Description"),
                            "LineAmount": line.get("Amount"),
                            "ExpenseAccount": line.get("AccountBasedExpenseLineDetail", {})
                            .get("AccountRef", {})
                            .get("name", ""),
                            "GlobalTaxCalculation": record.get("GlobalTaxCalculation"),
                            "TaxLines": tax_lines_str,
                        }
            else:
                chat_log.error(f"Unsupported entity type: {entity_type}")

            if parsed_record:
                parsed_records.append(parsed_record)
                chat_log.debug(f"Record parsed:\n {parsed_record}")
        df = pd.DataFrame(parsed_records)
        chat_log.info(f"Records parsed:\n {parsed_records}")
        return df

    def _check_and_update_vendor_details(self, vendor_record, input_name, input_address, chat_log):
        is_to_update = False
        # Check if the name is different from the vendor record
        if vendor_record.get("DisplayName") != input_name:
            # Update the vendor record
            is_to_update = True
            chat_log.debug(
                f"Updating vendor {vendor_record['Id']} name: {vendor_record['DisplayName']} -> {input_name}"
            )
        # Check if the address is different from the vendor record
        vendor_address = (
            vendor_record.get("BillAddr", {}).get("Line1", "")
            if vendor_record.get("BillAddr") and not pd.isna(vendor_record.get("BillAddr"))
            else ""
        )
        if vendor_address != input_address:
            # Update the vendor record
            is_to_update = True
            chat_log.debug(f"Updating vendor {vendor_record['Id']} address: {vendor_address} -> {input_address}")

        if not is_to_update:
            chat_log.debug(f"No change needed for Vendor {vendor_record['Id']}")
            return

        # Update the vendor record
        payload = {
            "Id": vendor_record["Id"],
            "SyncToken": vendor_record["SyncToken"],
            "DisplayName": input_name,
            "BillAddr": {"Line1": input_address},
        }

        res = quickbooks_update_vendor(payload)
        if res.get("success"):
            chat_log.info(f"Updated vendor {vendor_record['Id']}")
        else:
            chat_log.error(f"Failed to update vendor {vendor_record['Id']}: {res.get('message')}")

    """
    Generate reference_key from vendor name,transaction date, timestamp and invoice number
    """

    def generate_reference_key(
        self, vendor_name: str, transaction_date: str, timestamp: str, invoice_number: str, chat_log
    ):
        try:
            key_input = vendor_name + transaction_date
            # convert invoice number to string
            invoice_number = str(invoice_number)
            # Validate and process timestamp if provided
            validated_timestamp = self._validate_timestamp_format(timestamp)
            if validated_timestamp:
                chat_log.debug(f"Timestamp '{timestamp}' validated and normalized to '{validated_timestamp}'")
                # Give priority to timestamp - append to key_input for uniqueness if valid
                key_input += validated_timestamp
            # If timestamp is invalid, we use invoice number instead
            else:
                chat_log.debug(f"Timestamp '{timestamp}' is invalid. Using invoice number '{invoice_number}' instead.")
                key_input += invoice_number.strip() if invoice_number else ""

            chat_log.debug(f"Key input for reference key: '{key_input}'")
            return xxhash.xxh64(key_input).hexdigest().upper()
        except Exception as e:
            chat_log.error(f"Error generating reference key: {e} \n{traceback.format_exc()}")
            return ""

    def _validate_timestamp_format(self, timestamp: str) -> str:
        """
        Validate timestamp format and return normalized timestamp.
        Supports only 24-hour format:
        - HH:MM (24-hour format)
        - HH:MM:SS (24-hour format with seconds)
        - H:MM or H:MM:SS (single digit hour)

        Returns:
            str: Normalized timestamp in HH:MM:SS format, or empty string if invalid
        """
        # if timestamp is null or nan, then return empty string
        if pd.isna(timestamp) or not timestamp:
            return ""

        # Remove extra whitespace
        timestamp = timestamp.strip()

        # Pattern for 24-hour format: HH:MM or HH:MM:SS (with optional single digit hour)
        pattern = r"^([0-1]?[0-9]|2[0-3]):([0-5][0-9])(?::([0-5][0-9]))?$"

        match = re.match(pattern, timestamp)
        if match:
            hour = int(match.group(1))
            minute = int(match.group(2))
            second = int(match.group(3)) if match.group(3) else 0

            # Validate time components
            if 0 <= hour <= 23 and 0 <= minute <= 59 and 0 <= second <= 59:
                return f"{hour:02d}:{minute:02d}:{second:02d}"

        # Invalid timestamp format
        return ""
